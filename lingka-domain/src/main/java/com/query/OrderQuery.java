package com.query;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.common.constant.OrderStage;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 订单对象 tb_order
 *
 * <AUTHOR>
 * @date 2025-09-06
 */

public class OrderQuery extends Page {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 桌子ID
     */
    private Long tableId;

    /**
     * 购物车IDS
     */
    private List<Long> userCartIds;

    /**
     * 订单号
     */
    private String code;

    /**
     * 类型(ticket:门票 product:普通点单 diy:DIY)
     */
    private String type;

    /** 原金额 */
    private BigDecimal originalAmount;

    /** 最终金额 */
    private BigDecimal finalAmount;

    /** 减免金额 */
    private BigDecimal reducedAmount;

    /** 原金额(字符串) */
    private BigDecimal originalAmountStr;

    /** 最终金额(字符串) */
    private BigDecimal finalAmountStr;

    /** 减免金额(字符串) */
    private BigDecimal reducedAmountStr;

    /**
     * 订单状态
     */
    private String orderStage;

    /**
     * 订单状态
     */
    private List<String> orderStages;

    /** 二级订单状态 */
    private String subOrderStage;

    /** 二级订单状态 */
    private String subOrderStageName;

    /**
     * 支付状态
     */
    private String payStage;

    /**
     * 门票列表
     */
    private OrderTicketQuery orderTicket;

    /**
     * 门票详情列表
     */
    private List<OrderTicketDetailQuery> orderTicketDetailList;

    /**
     * 下单时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date orderTime;

    /**
     * 最小下单时间
     */
    private Date minOrderTime;

    /**
     * 最大下单时间
     */
    private Date maxOrderTime;

    /**
     * 支付时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date payTime;

    /**
     * 用户备注
     */
    private String userComment;

    /**
     * 用户订单状态(0:正常 1:无效)
     */
    private String userStatus;

    /**
     * 状态(0:正常 1:无效)
     */
    private String status;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date modifyTime;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setTableId(Long tableId) {
        this.tableId = tableId;
    }

    public Long getTableId() {
        return tableId;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getType() {
        return type;
    }

    public void setOrderStage(String orderStage) {
        this.orderStage = orderStage;
    }

    public String getOrderStage() {
        return orderStage;
    }

    public void setPayStage(String payStage) {
        this.payStage = payStage;
    }

    public String getPayStage() {
        return payStage;
    }

    public void setOrderTime(Date orderTime) {
        this.orderTime = orderTime;
    }

    public Date getOrderTime() {
        return orderTime;
    }

    public void setPayTime(Date payTime) {
        this.payTime = payTime;
    }

    public Date getPayTime() {
        return payTime;
    }

    public void setUserComment(String userComment) {
        this.userComment = userComment;
    }

    public String getUserComment() {
        return userComment;
    }

    public void setUserStatus(String userStatus) {
        this.userStatus = userStatus;
    }

    public String getUserStatus() {
        return userStatus;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public List<Long> getUserCartIds() {
        return userCartIds;
    }

    public void setUserCartIds(List<Long> userCartIds) {
        this.userCartIds = userCartIds;
    }

    public String getSubOrderStage() {
        return subOrderStage;
    }

    public void setSubOrderStage(String subOrderStage) {
        this.subOrderStage = subOrderStage;
    }

    public OrderTicketQuery getOrderTicket() {
        return orderTicket;
    }

    public void setOrderTicket(OrderTicketQuery orderTicket) {
        this.orderTicket = orderTicket;
    }

    public List<OrderTicketDetailQuery> getOrderTicketDetailList() {
        return orderTicketDetailList;
    }

    public void setOrderTicketDetailList(List<OrderTicketDetailQuery> orderTicketDetailList) {
        this.orderTicketDetailList = orderTicketDetailList;
    }

    public String getSubOrderStageName() {
        return subOrderStageName;
    }

    public void setSubOrderStageName(String subOrderStageName) {
        this.subOrderStageName = subOrderStageName;
    }

    public Date getMinOrderTime() {
        return minOrderTime;
    }

    public void setMinOrderTime(Date minOrderTime) {
        this.minOrderTime = minOrderTime;
    }

    public Date getMaxOrderTime() {
        return maxOrderTime;
    }

    public void setMaxOrderTime(Date maxOrderTime) {
        this.maxOrderTime = maxOrderTime;
    }

    public List<String> getOrderStages() {
        return orderStages;
    }

    public void setOrderStages(List<String> orderStages) {
        this.orderStages = orderStages;
    }

    public BigDecimal getOriginalAmount() {
        return originalAmount;
    }

    public void setOriginalAmount(BigDecimal originalAmount) {
        this.originalAmount = originalAmount;
    }

    public BigDecimal getFinalAmount() {
        return finalAmount;
    }

    public void setFinalAmount(BigDecimal finalAmount) {
        this.finalAmount = finalAmount;
    }

    public BigDecimal getReducedAmount() {
        return reducedAmount;
    }

    public void setReducedAmount(BigDecimal reducedAmount) {
        this.reducedAmount = reducedAmount;
    }

    public BigDecimal getOriginalAmountStr() {
        return originalAmountStr;
    }

    public void setOriginalAmountStr(BigDecimal originalAmountStr) {
        this.originalAmountStr = originalAmountStr;
    }

    public BigDecimal getFinalAmountStr() {
        return finalAmountStr;
    }

    public void setFinalAmountStr(BigDecimal finalAmountStr) {
        this.finalAmountStr = finalAmountStr;
    }

    public BigDecimal getReducedAmountStr() {
        return reducedAmountStr;
    }

    public void setReducedAmountStr(BigDecimal reducedAmountStr) {
        this.reducedAmountStr = reducedAmountStr;
    }
}
