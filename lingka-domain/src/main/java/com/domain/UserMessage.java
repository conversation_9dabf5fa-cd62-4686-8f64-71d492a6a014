package com.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 用户消息对象 tb_user_message
 * 
 * <AUTHOR>
 * @date 2025-09-22
 */

public class UserMessage extends Base {

    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 用户ID */
    private Long userId;

    /** 店铺ID */
    private Long shopId;

    /** 消息标题 */
    private String title;

    /** 消息内容 */
    private String content;

    /** 业务分类 */
    private String businessCatalog;

    /** 业务ID */
    private Long businessId;

    /** 是否已读(0:已读 1:未读) */
    private String readStage;

    /** 状态(0:正常 1:无效) */
    private String status;

    /** 修改时间 */
    private Date modifyTime;

    /** 创建时间 */
    private Date createTime;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }
    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getUserId() {
        return userId;
    }
    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public Long getShopId() {
        return shopId;
    }
    public void setTitle(String title) {
        this.title = title;
    }

    public String getTitle() {
        return title;
    }
    public void setContent(String content) {
        this.content = content;
    }

    public String getContent() {
        return content;
    }
    public void setBusinessCatalog(String businessCatalog) {
        this.businessCatalog = businessCatalog;
    }

    public String getBusinessCatalog() {
        return businessCatalog;
    }
    public void setBusinessId(Long businessId) {
        this.businessId = businessId;
    }

    public Long getBusinessId() {
        return businessId;
    }
    public void setReadStage(String readStage) {
        this.readStage = readStage;
    }

    public String getReadStage() {
        return readStage;
    }
    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }
    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public Date getModifyTime() {
        return modifyTime;
    }
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

}
