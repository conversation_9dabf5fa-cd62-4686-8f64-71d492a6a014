package com.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 订单产品对象 tb_order_product
 * 
 * <AUTHOR>
 * @date 2025-09-20
 */

public class OrderProduct extends Base {

    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 订单ID */
    private Long orderId;

    /** 产品ID */
    private Long productId;

    /** 产品名字 */
    private String productName;

    /** 产品封面 */
    private String productCover;

    /** 数量 */
    private Integer number;

    /** 原金额 */
    private BigDecimal originalAmount;

    /** 最终金额 */
    private BigDecimal finalAmount;

    /** 减免金额 */
    private BigDecimal reducedAmount;

    /** SKU */
    private String productSkuName;

    /** 规格 */
    private String productOptionName;

    /** 小料 */
    private String productIngredientName;

    /** 状态(0:正常 1:无效) */
    private String status;

    /** 修改时间 */
    private Date modifyTime;

    /** 创建时间 */
    private Date createTime;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }
    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public Long getOrderId() {
        return orderId;
    }
    public void setProductId(Long productId) {
        this.productId = productId;
    }

    public Long getProductId() {
        return productId;
    }
    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getProductName() {
        return productName;
    }
    public void setProductCover(String productCover) {
        this.productCover = productCover;
    }

    public String getProductCover() {
        return productCover;
    }
    public void setNumber(Integer number) {
        this.number = number;
    }

    public Integer getNumber() {
        return number;
    }
    public void setOriginalAmount(BigDecimal originalAmount) {
        this.originalAmount = originalAmount;
    }

    public BigDecimal getOriginalAmount() {
        return originalAmount;
    }
    public void setFinalAmount(BigDecimal finalAmount) {
        this.finalAmount = finalAmount;
    }

    public BigDecimal getFinalAmount() {
        return finalAmount;
    }
    public void setReducedAmount(BigDecimal reducedAmount) {
        this.reducedAmount = reducedAmount;
    }

    public BigDecimal getReducedAmount() {
        return reducedAmount;
    }
    public void setProductSkuName(String productSkuName) {
        this.productSkuName = productSkuName;
    }

    public String getProductSkuName() {
        return productSkuName;
    }
    public void setProductOptionName(String productOptionName) {
        this.productOptionName = productOptionName;
    }

    public String getProductOptionName() {
        return productOptionName;
    }
    public void setProductIngredientName(String productIngredientName) {
        this.productIngredientName = productIngredientName;
    }

    public String getProductIngredientName() {
        return productIngredientName;
    }
    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }
    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public Date getModifyTime() {
        return modifyTime;
    }
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

}
