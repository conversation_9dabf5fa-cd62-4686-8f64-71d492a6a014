package com.dto;

import java.util.List;

import com.common.bean.Bean;

/**
 * 用户购物车对象 tb_user_cart
 * 
 * <AUTHOR>
 * @date 2025-08-30
 */

public class CartDTO extends Bean{

    private static final long serialVersionUID = 1L;

    /** 总价 */
    private String totalOriginalPriceStr;

    /** 减免价 */
    private String totalReducedPriceStr;

    /** 最终价 */
    private String totalFinalPriceStr;

    /** 列表 */
    private List<UserCartDTO> userCarts;

    public String getTotalOriginalPriceStr() {
        return totalOriginalPriceStr;
    }

    public void setTotalOriginalPriceStr(String totalOriginalPriceStr) {
        this.totalOriginalPriceStr = totalOriginalPriceStr;
    }

    public String getTotalReducedPriceStr() {
        return totalReducedPriceStr;
    }

    public void setTotalReducedPriceStr(String totalReducedPriceStr) {
        this.totalReducedPriceStr = totalReducedPriceStr;
    }

    public String getTotalFinalPriceStr() {
        return totalFinalPriceStr;
    }

    public void setTotalFinalPriceStr(String totalFinalPriceStr) {
        this.totalFinalPriceStr = totalFinalPriceStr;
    }

    public List<UserCartDTO> getUserCarts() {
        return userCarts;
    }

    public void setUserCarts(List<UserCartDTO> userCarts) {
        this.userCarts = userCarts;
    }
}
