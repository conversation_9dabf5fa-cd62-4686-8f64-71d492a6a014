package com.dto;

import com.common.bean.Bean;

import java.util.Date;
import java.util.List;

import com.common.constant.App;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 店铺配置对象 tb_shop_config
 *
 * <AUTHOR>
 */

public class ShopConfigDTO extends Bean {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 店铺 ID
     */
    private Long shopId;

    /**
     * 店铺名称
     */
    private String name;

    /**
     * 店铺logo
     */
    private String logo;

    /**
     * 店铺联系方式
     */
    private String phone;

    /**
     * 店铺介绍头图文件
     */
    private String photo;

    /**
     * 店铺介绍描述
     */
    private String description;

    /**
     * 店铺标签
     */
    private String tags;

    /**
     * 是否对外展示 (0: 展示 1: 隐藏)
     */
    private String displaySwitch;

    /**
     * 门票自动审核开关(0:开启 1:关闭)
     */
    private String ticketAutoAuditSwitch;

    /**
     * 营业状态(open:营业 close:暂停营业 close_manual:暂停营业(手动))
     */
    private String state;

    /**
     * 营业时间
     */
    private List<ShopOpenCloseTimeDTO> shippingTimeList;

    /**
     * 省编号
     */
    private Long provinceId;

    /**
     * 市编号
     */
    private Long cityId;

    /**
     * 区编号
     */
    private Long distinctId;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 纬度
     */
    private Double latitude;

    /**
     * 经度
     */
    private Double longitude;

    /**
     * 状态(0:正常 1:无效)
     */
    private String status;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date modifyTime;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public void setLogo(String logo) {
        this.logo = logo;
    }

    public String getLogo() {
        return logo;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhoto(String photo) {
        this.photo = photo;
    }

    public String[] getPhoto() {
        return photo != null && !photo.isEmpty() ? photo.split(App.COMMA) : null;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }

    public void setTags(String tags) {
        this.tags = tags;
    }

    public String[] getTags() {
        return tags != null && !tags.isEmpty() ? tags.split(App.COMMA) : null;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getState() {
        return state;
    }

    public void setProvinceId(Long provinceId) {
        this.provinceId = provinceId;
    }

    public Long getProvinceId() {
        return provinceId;
    }

    public void setCityId(Long cityId) {
        this.cityId = cityId;
    }

    public Long getCityId() {
        return cityId;
    }

    public void setDistinctId(Long distinctId) {
        this.distinctId = distinctId;
    }

    public Long getDistinctId() {
        return distinctId;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getAddress() {
        return address;
    }

    public void setLatitude(Double latitude) {
        this.latitude = latitude;
    }

    public Double getLatitude() {
        return latitude;
    }

    public void setLongitude(Double longitude) {
        this.longitude = longitude;
    }

    public Double getLongitude() {
        return longitude;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public String getDisplaySwitch() {
        return displaySwitch;
    }

    public void setDisplaySwitch(String displaySwitch) {
        this.displaySwitch = displaySwitch;
    }

    public List<ShopOpenCloseTimeDTO> getShippingTimeList() {
        return shippingTimeList;
    }

    public void setShippingTimeList(List<ShopOpenCloseTimeDTO> shippingTimeList) {
        this.shippingTimeList = shippingTimeList;
    }

    public String getTicketAutoAuditSwitch() {
        return ticketAutoAuditSwitch;
    }

    public void setTicketAutoAuditSwitch(String ticketAutoAuditSwitch) {
        this.ticketAutoAuditSwitch = ticketAutoAuditSwitch;
    }
}
