package com.dto;

import com.common.bean.Bean;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 用户购物车对象 tb_user_cart
 * 
 * <AUTHOR>
 * @date 2025-08-30
 */

public class UserCartDTO extends Bean{

    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 用户ID */
    private Long userId;

    /** 店铺ID */
    private Long shopId;

    /** 产品ID */
    private Long productId;

    /** 产品封面 */
    private String productCover;

    private String productName;

    /** 产品SKU ID */
    private Long productSkuId;

    /** 产品SKU 价格 */
    private BigDecimal productSkuPrice;

    /** 产品SKU+小料价格 */
    private BigDecimal productSkuIngredientPrice;

    /** 产品SKU+小料价格 */
    private String productSkuIngredientPriceStr;

    /** 产品SKU名字 */
    private String productSkuName;

    /** 产品选项IDS */
    private String productOptionIds;

    private List<Long> productOptionIdList;

    private List<String> productOptionNameList;

    /** 产品小料IDS */
    private String productIngredientIds;

    /** 产品小料IDS */
    private List<Long> productIngredientIdList;

    /** 产品小料IDS */
    private List<String> productIngredientNameList;

    /** 数量 */
    private Integer number;

    /** 用户购物车状态(0:有效 1:无效) */
    private String userCartStatus;

    /** 原价格 */
    private BigDecimal originalPrice;

    /** 最终价格 */
    private BigDecimal finalPrice;

    /** 减免价格 */
    private BigDecimal reducedPrice;

    /** 原价格 */
    private String originalPriceStr;

    /** 最终价格 */
    private String finalPriceStr;

    /** 减免价格 */
    private String reducedPriceStr;

    /** 状态(0:正常 1:无效) */
    private String status;

    /** 修改时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date modifyTime;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date createTime;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }
    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getUserId() {
        return userId;
    }
    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public Long getShopId() {
        return shopId;
    }
    public void setProductId(Long productId) {
        this.productId = productId;
    }

    public Long getProductId() {
        return productId;
    }
    public void setProductSkuId(Long productSkuId) {
        this.productSkuId = productSkuId;
    }

    public Long getProductSkuId() {
        return productSkuId;
    }
    public void setProductOptionIds(String productOptionIds) {
        this.productOptionIds = productOptionIds;
    }

    public String getProductOptionIds() {
        return productOptionIds;
    }
    public void setProductIngredientIds(String productIngredientIds) {
        this.productIngredientIds = productIngredientIds;
    }

    public String getProductIngredientIds() {
        return productIngredientIds;
    }
    public void setNumber(Integer number) {
        this.number = number;
    }

    public Integer getNumber() {
        return number;
    }
    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }
    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public Date getModifyTime() {
        return modifyTime;
    }
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public String getUserCartStatus() {
        return userCartStatus;
    }

    public void setUserCartStatus(String userCartStatus) {
        this.userCartStatus = userCartStatus;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public List<Long> getProductOptionIdList() {
        return productOptionIdList;
    }

    public void setProductOptionIdList(List<Long> productOptionIdList) {
        this.productOptionIdList = productOptionIdList;
    }

    public List<String> getProductOptionNameList() {
        return productOptionNameList;
    }

    public void setProductOptionNameList(List<String> productOptionNameList) {
        this.productOptionNameList = productOptionNameList;
    }

    public List<Long> getProductIngredientIdList() {
        return productIngredientIdList;
    }

    public void setProductIngredientIdList(List<Long> productIngredientIdList) {
        this.productIngredientIdList = productIngredientIdList;
    }

    public List<String> getProductIngredientNameList() {
        return productIngredientNameList;
    }

    public void setProductIngredientNameList(List<String> productIngredientNameList) {
        this.productIngredientNameList = productIngredientNameList;
    }

    public String getProductSkuName() {
        return productSkuName;
    }

    public void setProductSkuName(String productSkuName) {
        this.productSkuName = productSkuName;
    }

    public BigDecimal getOriginalPrice() {
        return originalPrice;
    }

    public void setOriginalPrice(BigDecimal originalPrice) {
        this.originalPrice = originalPrice;
    }

    public String getProductCover() {
        return productCover;
    }

    public BigDecimal getProductSkuPrice() {
        return productSkuPrice;
    }

    public void setProductSkuPrice(BigDecimal productSkuPrice) {
        this.productSkuPrice = productSkuPrice;
    }

    public void setProductCover(String productCover) {
        this.productCover = productCover;
    }

    public BigDecimal getProductSkuIngredientPrice() {
        return productSkuIngredientPrice;
    }

    public void setProductSkuIngredientPrice(BigDecimal productSkuIngredientPrice) {
        this.productSkuIngredientPrice = productSkuIngredientPrice;
    }

    public BigDecimal getFinalPrice() {
        return finalPrice;
    }

    public void setFinalPrice(BigDecimal finalPrice) {
        this.finalPrice = finalPrice;
    }

    public BigDecimal getReducedPrice() {
        return reducedPrice;
    }

    public void setReducedPrice(BigDecimal reducedPrice) {
        this.reducedPrice = reducedPrice;
    }

    public String getOriginalPriceStr() {
        return originalPriceStr;
    }

    public void setOriginalPriceStr(String originalPriceStr) {
        this.originalPriceStr = originalPriceStr;
    }

    public String getFinalPriceStr() {
        return finalPriceStr;
    }

    public void setFinalPriceStr(String finalPriceStr) {
        this.finalPriceStr = finalPriceStr;
    }

    public String getReducedPriceStr() {
        return reducedPriceStr;
    }

    public void setReducedPriceStr(String reducedPriceStr) {
        this.reducedPriceStr = reducedPriceStr;
    }

    public String getProductSkuIngredientPriceStr() {
        return productSkuIngredientPriceStr;
    }

    public void setProductSkuIngredientPriceStr(String productSkuIngredientPriceStr) {
        this.productSkuIngredientPriceStr = productSkuIngredientPriceStr;
    }
}
