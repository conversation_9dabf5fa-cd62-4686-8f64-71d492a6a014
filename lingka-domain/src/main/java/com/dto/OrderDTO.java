package com.dto;

import com.common.bean.Bean;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 订单对象 tb_order
 * 
 * <AUTHOR>
 * @date 2025-09-06
 */

public class OrderDTO extends Bean{

    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 店铺ID */
    private Long shopId;

    /** 用户ID */
    private Long userId;

    /** 用户 */
    private UserDTO user;

    /** 桌子ID */
    private Long tableId;

    /** 订单号 */
    private String code;

    /** 类型(ticket:门票 product:普通点单 diy:DIY) */
    private String type;

    /** 类型名称 */
    private String typeName;

    /** 原金额 */
    private BigDecimal originalAmount;

    /** 最终金额 */
    private BigDecimal finalAmount;

    /** 减免金额 */
    private BigDecimal reducedAmount;

    /** 原金额(字符串) */
    private BigDecimal originalAmountStr;

    /** 最终金额(字符串) */
    private BigDecimal finalAmountStr;

    /** 减免金额(字符串) */
    private BigDecimal reducedAmountStr;

    /** 订单状态 */
    private String orderStage;

    /** 订单状态名称 */
    private String orderStageName;

    /** 二级订单状态 */
    private String subOrderStage;

    /** 二级订单状态 */
    private String subOrderStageName;

    /** 支付状态 */
    private String payStage;

    /** 支付状态名称 */
    private String payStageName;

    /** 订单产品 */
    private List<OrderProductDTO> orderProductList;

    /** 订单门票 */
    private OrderTicketDTO orderTicket;

    /** 订单门票详情 */
    private List<OrderTicketDetailDTO> orderTicketDetailList;

    private ShopConfigDTO shopConfig;

    /** 下单时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date orderTime;

    /** 支付时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date payTime;

    /** 用户备注 */
    private String userComment;

    /** 用户订单状态(0:正常 1:无效) */
    private String userStatus;

    /** 状态(0:正常 1:无效) */
    private String status;

    /** 修改时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date modifyTime;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date createTime;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }
    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public Long getShopId() {
        return shopId;
    }
    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getUserId() {
        return userId;
    }
    public void setTableId(Long tableId) {
        this.tableId = tableId;
    }

    public Long getTableId() {
        return tableId;
    }
    public void setCode(String code) {
        this.code = code;
    }

    public String getCode() {
        return code;
    }
    public void setType(String type) {
        this.type = type;
    }

    public String getType() {
        return type;
    }
    public void setOrderStage(String orderStage) {
        this.orderStage = orderStage;
    }

    public String getOrderStage() {
        return orderStage;
    }
    public void setPayStage(String payStage) {
        this.payStage = payStage;
    }

    public String getPayStage() {
        return payStage;
    }
    public void setOrderTime(Date orderTime) {
        this.orderTime = orderTime;
    }

    public Date getOrderTime() {
        return orderTime;
    }
    public void setPayTime(Date payTime) {
        this.payTime = payTime;
    }

    public Date getPayTime() {
        return payTime;
    }
    public void setUserComment(String userComment) {
        this.userComment = userComment;
    }

    public String getUserComment() {
        return userComment;
    }
    public void setUserStatus(String userStatus) {
        this.userStatus = userStatus;
    }

    public String getUserStatus() {
        return userStatus;
    }
    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }
    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public Date getModifyTime() {
        return modifyTime;
    }
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    public String getOrderStageName() {
        return orderStageName;
    }

    public void setOrderStageName(String orderStageName) {
        this.orderStageName = orderStageName;
    }

    public String getPayStageName() {
        return payStageName;
    }

    public void setPayStageName(String payStageName) {
        this.payStageName = payStageName;
    }

    public List<OrderProductDTO> getOrderProductList() {
        return orderProductList;
    }

    public void setOrderProductList(List<OrderProductDTO> orderProductList) {
        this.orderProductList = orderProductList;
    }

    public OrderTicketDTO getOrderTicket() {
        return orderTicket;
    }

    public void setOrderTicket(OrderTicketDTO orderTicket) {
        this.orderTicket = orderTicket;
    }

    public List<OrderTicketDetailDTO> getOrderTicketDetailList() {
        return orderTicketDetailList;
    }

    public void setOrderTicketDetailList(List<OrderTicketDetailDTO> orderTicketDetailList) {
        this.orderTicketDetailList = orderTicketDetailList;
    }

    public String getSubOrderStage() {
        return subOrderStage;
    }

    public void setSubOrderStage(String subOrderStage) {
        this.subOrderStage = subOrderStage;
    }

    public String getSubOrderStageName() {
        return subOrderStageName;
    }

    public void setSubOrderStageName(String subOrderStageName) {
        this.subOrderStageName = subOrderStageName;
    }

    public ShopConfigDTO getShopConfig() {
        return shopConfig;
    }

    public void setShopConfig(ShopConfigDTO shopConfig) {
        this.shopConfig = shopConfig;
    }

    public BigDecimal getOriginalAmount() {
        return originalAmount;
    }

    public void setOriginalAmount(BigDecimal originalAmount) {
        this.originalAmount = originalAmount;
    }

    public BigDecimal getFinalAmount() {
        return finalAmount;
    }

    public void setFinalAmount(BigDecimal finalAmount) {
        this.finalAmount = finalAmount;
    }

    public BigDecimal getReducedAmount() {
        return reducedAmount;
    }

    public void setReducedAmount(BigDecimal reducedAmount) {
        this.reducedAmount = reducedAmount;
    }

    public BigDecimal getOriginalAmountStr() {
        return originalAmountStr;
    }

    public void setOriginalAmountStr(BigDecimal originalAmountStr) {
        this.originalAmountStr = originalAmountStr;
    }

    public BigDecimal getFinalAmountStr() {
        return finalAmountStr;
    }

    public void setFinalAmountStr(BigDecimal finalAmountStr) {
        this.finalAmountStr = finalAmountStr;
    }

    public BigDecimal getReducedAmountStr() {
        return reducedAmountStr;
    }

    public void setReducedAmountStr(BigDecimal reducedAmountStr) {
        this.reducedAmountStr = reducedAmountStr;
    }

    public UserDTO getUser() {
        return user;
    }

    public void setUser(UserDTO user) {
        this.user = user;
    }
}
