package com.dto;

import com.common.bean.Bean;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 订单支付对象 tb_order_payment
 * 
 * <AUTHOR>
 * @date 2025-09-27
 */

public class OrderPaymentDTO extends Bean{

    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 店铺ID */
    private Long shopId;

    /** 订单ID */
    private Long orderId;

    /** 订单号 */
    private String paymentCode;

    /** 预交易ID */
    private String prepayId;

    /** 请求头 */
    private String requestHeader;

    /** 请求参数 */
    private String requestContent;

    /** 返回参数 */
    private String responseContent;

    /** 支付状态(0:未支付 1:已支付 2:已退款) */
    private String payStage;

    /** 失效时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date expireTime;

    /** 状态(0:正常 1:无效) */
    private String status;

    /** 修改时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date modifyTime;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date createTime;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }
    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public Long getShopId() {
        return shopId;
    }
    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public Long getOrderId() {
        return orderId;
    }
    public void setPaymentCode(String paymentCode) {
        this.paymentCode = paymentCode;
    }

    public String getPaymentCode() {
        return paymentCode;
    }
    public void setPrepayId(String prepayId) {
        this.prepayId = prepayId;
    }

    public String getPrepayId() {
        return prepayId;
    }
    public void setRequestHeader(String requestHeader) {
        this.requestHeader = requestHeader;
    }

    public String getRequestHeader() {
        return requestHeader;
    }
    public void setRequestContent(String requestContent) {
        this.requestContent = requestContent;
    }

    public String getRequestContent() {
        return requestContent;
    }
    public void setResponseContent(String responseContent) {
        this.responseContent = responseContent;
    }

    public String getResponseContent() {
        return responseContent;
    }
    public void setPayStage(String payStage) {
        this.payStage = payStage;
    }

    public String getPayStage() {
        return payStage;
    }
    public void setExpireTime(Date expireTime) {
        this.expireTime = expireTime;
    }

    public Date getExpireTime() {
        return expireTime;
    }
    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }
    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public Date getModifyTime() {
        return modifyTime;
    }
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

}
