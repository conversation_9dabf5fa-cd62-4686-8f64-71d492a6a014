package com.dto;

import com.common.bean.Bean;

import java.math.BigDecimal;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 订单产品对象 tb_order_product
 *
 * <AUTHOR>
 * @date 2025-09-06
 */

public class OrderProductDTO extends Bean {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 产品ID
     */
    private Long productId;

    /**
     * 产品名字
     */
    private String productName;

    /**
     * 产品封面
     */
    private String productCover;

    /**
     * 数量
     */
    private Integer number;

    /** 原金额 */
    private BigDecimal originalAmount;

    /** 最终金额 */
    private BigDecimal finalAmount;

    /** 减免金额 */
    private BigDecimal reducedAmount;

    /** 原金额(字符串) */
    private BigDecimal originalAmountStr;

    /** 最终金额(字符串) */
    private BigDecimal finalAmountStr;

    /** 减免金额(字符串) */
    private BigDecimal reducedAmountStr;

    /**
     * SKU
     */
    private String productSkuName;

    /**
     * 规格
     */
    private String productOptionName;

    /**
     * 小料
     */
    private String productIngredientName;

    /**
     * 状态(0:正常 1:无效)
     */
    private String status;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date modifyTime;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setProductId(Long productId) {
        this.productId = productId;
    }

    public Long getProductId() {
        return productId;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getProductCover() {
        return productCover;
    }

    public void setProductCover(String productCover) {
        this.productCover = productCover;
    }

    public void setNumber(Integer number) {
        this.number = number;
    }

    public Integer getNumber() {
        return number;
    }

    public BigDecimal getOriginalAmount() {
        return originalAmount;
    }

    public void setOriginalAmount(BigDecimal originalAmount) {
        this.originalAmount = originalAmount;
    }

    public BigDecimal getFinalAmount() {
        return finalAmount;
    }

    public void setFinalAmount(BigDecimal finalAmount) {
        this.finalAmount = finalAmount;
    }

    public BigDecimal getReducedAmount() {
        return reducedAmount;
    }

    public void setReducedAmount(BigDecimal reducedAmount) {
        this.reducedAmount = reducedAmount;
    }

    public void setProductSkuName(String productSkuName) {
        this.productSkuName = productSkuName;
    }

    public String getProductSkuName() {
        return productSkuName;
    }

    public void setProductOptionName(String productOptionName) {
        this.productOptionName = productOptionName;
    }

    public String getProductOptionName() {
        return productOptionName;
    }

    public void setProductIngredientName(String productIngredientName) {
        this.productIngredientName = productIngredientName;
    }

    public String getProductIngredientName() {
        return productIngredientName;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public BigDecimal getOriginalAmountStr() {
        return originalAmountStr;
    }

    public void setOriginalAmountStr(BigDecimal originalAmountStr) {
        this.originalAmountStr = originalAmountStr;
    }

    public BigDecimal getFinalAmountStr() {
        return finalAmountStr;
    }

    public void setFinalAmountStr(BigDecimal finalAmountStr) {
        this.finalAmountStr = finalAmountStr;
    }

    public BigDecimal getReducedAmountStr() {
        return reducedAmountStr;
    }

    public void setReducedAmountStr(BigDecimal reducedAmountStr) {
        this.reducedAmountStr = reducedAmountStr;
    }
}
