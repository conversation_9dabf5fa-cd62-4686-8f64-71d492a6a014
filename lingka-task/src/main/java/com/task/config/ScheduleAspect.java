package com.task.config;

import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;

import com.task.sequence.LogSequence;


@Aspect
@Component
public class ScheduleAspect {
	private static final String REQUEST_ID = "requestId";
	@Pointcut(value = "@annotation(org.springframework.scheduling.annotation.Scheduled)")
	public void webLog(){}
	
	@Before("webLog()")
	public void doBefore(JoinPoint joinPoint) throws Throwable {
		MDC.put(REQUEST_ID, LogSequence.get());
	}
	
	@AfterReturning(returning = "response", pointcut = "webLog()")
	public void doAfterReturning(Object response) throws Throwable {
		MDC.remove(REQUEST_ID);
	}

}
