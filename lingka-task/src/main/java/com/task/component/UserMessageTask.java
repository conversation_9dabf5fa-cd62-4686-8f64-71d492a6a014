package com.task.component;

import java.util.Date;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.common.bean.UserMessagePayload;
import com.common.constant.CacheKey;
import com.common.constant.DataStatus;
import com.common.constant.UserMessageReadStage;
import com.domain.UserMessage;
import com.service.UserMessageService;

@Component
public class UserMessageTask extends BaseTask{

    @Autowired
    private UserMessageService userMessageService;

    @Autowired
    private RedisTemplate<String,String> redisTemplate;

    /**
     * 记录用户消息
     */
    @Scheduled(initialDelay = 100,fixedRate = 1000)
    public void start(){
        logger.info("record user message task start");
        try {
            while (true){
                String userMessagePayloadStr = redisTemplate.opsForList().rightPop(CacheKey.USER_MESSAGE_QUEUE);
                if (userMessagePayloadStr == null){
                    break;
                }
                // 将用户消息转为对象
                UserMessagePayload userMessagePayload = (UserMessagePayload) this.getObject(userMessagePayloadStr, UserMessagePayload.class);
                Date datetime = this.getServerTime();
                UserMessage userMessage = new UserMessage();
                userMessage.setUserId(userMessagePayload.getUserId());
                userMessage.setShopId(userMessagePayload.getShopId());
                userMessage.setTitle(userMessagePayload.getTitle());
                userMessage.setContent(userMessagePayload.getContent());
                userMessage.setBusinessCatalog(userMessagePayload.getBusinessCatalog());
                userMessage.setBusinessId(userMessagePayload.getBusinessId());
                userMessage.setReadStage(UserMessageReadStage.N.getCode());
                userMessage.setStatus(DataStatus.Y.getCode());
                userMessage.setModifyTime(datetime);
                userMessage.setCreateTime(datetime);
                // 保存用户消息
                userMessageService.create(userMessage);
            }
        }catch (Exception e){
            logger.error(e.getMessage(),e);
        }
        logger.info("record user message task end");
    }
}
