server.port=8081
spring.application.name=lingka-task
#数据库连接配置
spring.datasource.type=com.alibaba.druid.pool.DruidDataSource
spring.datasource.url=jdbc:mysql://***************:3306/lingka?characterEncoding=utf8&useSSL=false
spring.datasource.username=lingka
spring.datasource.password=lingka
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.druid.initialSize=1
spring.datasource.druid.minIdle=1
spring.datasource.druid.maxActive=10
spring.datasource.druid.maxWait=60000
spring.datasource.druid.timeBetweenEvictionRunsMillis=60000
spring.datasource.druid.minEvictableIdleTimeMillis=300000
spring.datasource.druid.validationQuery=SELECT 1 FROM DUAL
spring.datasource.druid.testWhileIdle=true
spring.datasource.druid.testOnBorrow=false
spring.datasource.druid.testOnReturn=false
spring.datasource.druid.poolPreparedStatements=true
spring.datasource.druid.maxPoolPreparedStatementPerConnectionSize=20
spring.datasource.druid.filters=stat,wall,slf4j
spring.datasource.druid.connectionProperties=druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
spring.datasource.druid.useGlobalDataSourceStat=true
# 文件上传配置
spring.filesystem.url=http://localhost:8080
spring.file.location=/Users/<USER>/develop/file/lingka-task
# 缓存配置
spring.redis.host=***************
spring.redis.password=qq#1259799716
spring.redis.port=6379
spring.redis.database=0
mybatis.mapper-locations=classpath:mapper/*.xml
logging.config=classpath:config/logback-spring.xml
logging.file.path=/Users/<USER>/develop/logs/dev/lingka-task