drop table if exists `tb_user_message` ;
create table `tb_user_message` (
    `id` bigint not null auto_increment comment '主键',
    `user_id` bigint comment '用户ID',
    `shop_id` bigint comment '店铺ID',
    `title` varchar(64) comment '消息标题',
    `content` varchar(500) comment '消息内容',
    `business_catalog` varchar(4) comment '业务分类',
    `business_id` bigint comment '业务ID',
    `read_stage` varchar(1) comment '是否已读(0:已读 1:未读)',
    `status` varchar(1) default '0' comment '状态(0:正常 1:无效)',
    `modify_time` datetime not null default current_timestamp on update current_timestamp comment '修改时间',
    `create_time` datetime not null default current_timestamp comment '创建时间',
    primary key(`id`)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_unicode_ci
auto_increment=10000000
comment '用户消息表';
alter table `tb_user_message` add index `idx_user_ticket_01` (`user_id`);
alter table `tb_user_message` add index `idx_user_ticket_02` (`shop_id`);