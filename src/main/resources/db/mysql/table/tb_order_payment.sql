drop table if exists `tb_order_payment` ;
create table `tb_order_payment` (
    `id` bigint not null auto_increment comment '主键',
    `shop_id` bigint comment '店铺ID',
    `order_id` bigint comment '订单ID',
    `payment_code` varchar(64) comment '订单号',
    `prepay_id` varchar(64) comment '预交易ID',
    `request_header` text comment '请求头',
    `request_content` text comment '请求参数',
    `response_content` text comment '返回参数',
    `pay_stage` varchar(1) comment '支付状态(0:未支付 1:已支付 2:已退款)',
    `expire_time` datetime comment '失效时间',
    `status` varchar(1) default '0' comment '状态(0:正常 1:无效)',
    `modify_time` datetime not null default current_timestamp on update current_timestamp comment '修改时间',
    `create_time` datetime not null default current_timestamp comment '创建时间',
    primary key(`id`)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_unicode_ci
auto_increment=10000000
comment '订单支付表';
alter table `tb_order_payment` add index `idx_payment_01` (`shop_id`);
alter table `tb_order_payment` add index `idx_order_payment_02` (`order_id`);
alter table `tb_order_payment` add unique index `idx_order_payment_03` (`payment_code`);
alter table `tb_order_payment` add index `idx_order_payment_04` (`prepay_id`);