drop table if exists `tb_order_product` ;
create table `tb_order_product` (
    `id` bigint not null auto_increment comment '主键',
    `order_id` bigint comment '订单ID',
    `product_id` bigint comment '产品ID',
    `product_name` varchar(20) comment '产品名字',
    `product_cover` varchar(500) comment '产品封面',
    `number` int comment '数量',
    `original_amount` decimal(10,2) comment '原金额',
    `final_amount` decimal(10,2) comment '最终金额',
    `reduced_amount` decimal(10,2) comment '减免金额',
    `product_sku_name` varchar(200) comment 'SKU',
    `product_option_name` varchar(200) comment '规格',
    `product_ingredient_name` varchar(200) comment '小料',
    `status` varchar(1) default '0' comment '状态(0:正常 1:无效)',
    `modify_time` datetime not null default current_timestamp on update current_timestamp comment '修改时间',
    `create_time` datetime not null default current_timestamp comment '创建时间',
    primary key(`id`)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_unicode_ci
auto_increment=10000000
comment '订单产品表';
alter table `tb_order_product` add index `idx_order_product_01` (`order_id`);
alter table `tb_order_product` add index `idx_order_product_02` (`product_id`);