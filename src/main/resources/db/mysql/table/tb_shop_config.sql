drop table if exists `tb_shop_config` ;
create table `tb_shop_config` (
    `id` bigint not null auto_increment comment '主键',
    `shop_id` bigint comment '店铺ID',
    `name` varchar(50) comment '店铺名称',
    `logo` varchar(64) comment '店铺logo',
    `phone` varchar(15) comment '店铺联系方式',
    `photo` varchar(330) comment '店铺介绍头图文件',
    `description` varchar(2000) comment '店铺介绍描述',
    `tags` varchar(2000) comment '店铺标签',
    `display_switch` varchar(1) default '1' comment '是否对外展示(0:展示 1:隐藏)',
    `ticket_auto_audit_switch` varchar(1) default '1' comment '门票自动审核开关(0:开启 1:关闭)',
    `state` varchar(20) comment '营业状态(open:营业 close:暂停营业 close_manual:暂停营业(手动))',
    `province_id` bigint comment '省编号',
    `city_id` bigint comment '市编号',
    `distinct_id` bigint comment '区编号',
    `address` varchar(200) comment '详细地址',
    `latitude` decimal(10, 7) comment '纬度',
    `longitude` decimal(10, 7) comment '经度',
    `status` varchar(1) comment '状态(0:正常 1:无效)',
    `modify_time` datetime not null default current_timestamp on update current_timestamp comment '修改时间',
    `create_time` datetime not null default current_timestamp comment '创建时间',
    primary key(`id`)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_unicode_ci
auto_increment=10000000
comment '店铺配置表';
alter table `tb_shop_config` add unique index `idx_shop_config_01` (`shop_id`);
alter table `tb_shop_config` add index `idx_shop_config_02` (`province_id`);
alter table `tb_shop_config` add index `idx_shop_config_03` (`city_id`);
alter table `tb_shop_config` add index `idx_shop_config_04` (`latitude`);
alter table `tb_shop_config` add index `idx_shop_config_05` (`longitude`);
