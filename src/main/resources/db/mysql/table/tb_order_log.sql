drop table if exists `tb_order_log` ;
create table `tb_order_log` (
    `id` bigint not null auto_increment comment '主键',
    `order_id` bigint comment '订单ID',
    `oper_user_id` bigint comment '操作人ID',
    `operation` varchar(20) comment '操作',
    `status` varchar(1) default '0' comment '状态(0:正常 1:无效)',
    `modify_time` datetime not null default current_timestamp on update current_timestamp comment '修改时间',
    `create_time` datetime not null default current_timestamp comment '创建时间',
    primary key(`id`)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_unicode_ci
auto_increment=10000000
comment '订单日志表';
alter table `tb_order_log` add index `idx_order_log_01` (`order_id`);
alter table `tb_order_log` add index `idx_order_log_02` (`oper_user_id`);