package com.service;

import java.util.List;
import java.util.Map;

import com.github.pagehelper.PageInfo;
import com.domain.UserMessage;
import com.dto.UserMessageDTO;
import com.query.UserMessageQuery;

/**
 * 用户消息Service接口
 * 
 * <AUTHOR>
 * @date 2025-09-21
 */
public interface UserMessageService {
    /**
     * 查询用户消息
     * 
     * @param id 用户消息ID
     * @return 用户消息
     */
    UserMessageDTO findById(Long id);

    /**
     * 查询用户消息列表
     *
     * @param ids 编号集合
     * @return 用户消息集合
     */
    List<UserMessageDTO> findByIds(List<Long> ids);

    /**
     * 查询用户消息列表
     * 
     * @param userMessageQuery 用户消息
     * @return 用户消息集合
     */
    List<UserMessageDTO> findAll(UserMessageQuery userMessageQuery);

	/**
	 *  分页查询用户消息列表
	 *
	 * @param userMessageQuery 用户消息
	 * @return 用户消息集合
	 */
	PageInfo<UserMessageDTO> find(UserMessageQuery userMessageQuery);

    /**
     * 查询用户消息Map
     *
     * @param ids 编号集合
     * @return 用户消息Map
     */
    Map<Long, UserMessageDTO> findMapByIds(List<Long> ids);

    /**
     * 新增用户消息
     * 
     * @param userMessage 用户消息
     * @return 结果
     */
    int create(UserMessage userMessage);

    /**
     * 修改用户消息
     * 
     * @param userMessage 用户消息
     * @return 结果
     */
    int modifyById(UserMessage userMessage);

    /**
     * 删除用户消息信息
     * 
     * @param id 用户消息id
     * @return 结果
     */
   int removeById(Long id);
}
