package com.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.dao.OrderPaymentDao;
import com.domain.OrderPayment;
import com.dto.OrderPaymentDTO;
import com.query.OrderPaymentQuery;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.service.OrderPaymentService;

/**
 * 订单支付Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-09-27
 */
@Service
public class OrderPaymentServiceImpl extends BaseService implements OrderPaymentService {

    @Autowired
    private OrderPaymentDao orderPaymentDao;

    /**
     * 查询订单支付
     * 
     * @param id 订单支付ID
     * @return 订单支付
     */
    @Override
    public OrderPaymentDTO findById(Long id) {
        return orderPaymentDao.selectById(id);
    }

    /**
     * 查询订单支付列表
     *
     * @param ids 编号集合
     * @return 订单支付集合
     */
    @Override
    public List<OrderPaymentDTO> findByIds(List<Long> ids) {
        return orderPaymentDao.selectByIds(ids);
    }

    /**
     * 查询订单支付列表
     *
     * @param orderPaymentQuery 订单支付
     * @return 订单支付
     */
    @Override
    public List<OrderPaymentDTO> findAll(OrderPaymentQuery orderPaymentQuery) {
        return orderPaymentDao.select(orderPaymentQuery);
    }

    @Override
    public OrderPaymentDTO findByMax(OrderPaymentQuery orderPaymentQuery) {
        return orderPaymentDao.selectByMax(orderPaymentQuery);
    }

    /**
	 * 分页查询订单支付列表
	 *
	 * @param orderPaymentQuery 订单支付
	 * @return 订单支付
	 */
	@Override
	public PageInfo<OrderPaymentDTO> find(OrderPaymentQuery orderPaymentQuery) {
        PageHelper.startPage(orderPaymentQuery.getPageNum(),orderPaymentQuery.getPageSize());
		List<OrderPaymentDTO> orderPaymentDTOList = orderPaymentDao.select(orderPaymentQuery);
		return new PageInfo<>(orderPaymentDTOList);
	}

    /**
     * 查询订单支付Map
     *
     * @param ids 编号集合
     * @return 订单支付Map
     */
    @Override
    public Map<Long, OrderPaymentDTO> findMapByIds(List<Long> ids) {
        Map<Long, OrderPaymentDTO> orderPaymentDTOMap = new HashMap<>();
        if (ids != null && !ids.isEmpty()){
            List<OrderPaymentDTO> orderPaymentDTOList =  orderPaymentDao.selectByIds(ids);
            for (OrderPaymentDTO orderPaymentDTO : orderPaymentDTOList) {
                    orderPaymentDTOMap.put(orderPaymentDTO.getId(),orderPaymentDTO);
            }
        }
        return orderPaymentDTOMap;
    }

    /**
     * 新增订单支付
     *
     * @param orderPayment 订单支付
     * @return 结果
     */
    @Override
    public int create(OrderPayment orderPayment) {
        return orderPaymentDao.insert(orderPayment);
    }

    /**
     * 修改订单支付
     *
     * @param orderPayment 订单支付
     * @return 结果
     */
    @Override
    public int modifyById(OrderPayment orderPayment) {
        return orderPaymentDao.updateById(orderPayment);
    }


    /**
     * 删除订单支付信息
     *
     * @param id 订单支付ID
     * @return 结果
     */
    @Override
    public int removeById(Long id) {
        return orderPaymentDao.deleteById(id);
    }

}
