package com.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.dao.UserMessageDao;
import com.domain.UserMessage;
import com.dto.UserMessageDTO;
import com.query.UserMessageQuery;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.service.UserMessageService;

/**
 * 用户消息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-09-21
 */
@Service
public class UserMessageServiceImpl extends BaseService implements UserMessageService {

    @Autowired
    private UserMessageDao userMessageDao;

    /**
     * 查询用户消息
     * 
     * @param id 用户消息ID
     * @return 用户消息
     */
    @Override
    public UserMessageDTO findById(Long id) {
        return userMessageDao.selectById(id);
    }

    /**
     * 查询用户消息列表
     *
     * @param ids 编号集合
     * @return 用户消息集合
     */
    @Override
    public List<UserMessageDTO> findByIds(List<Long> ids) {
        return userMessageDao.selectByIds(ids);
    }

    /**
     * 查询用户消息列表
     *
     * @param userMessageQuery 用户消息
     * @return 用户消息
     */
    @Override
    public List<UserMessageDTO> findAll(UserMessageQuery userMessageQuery) {
        return userMessageDao.select(userMessageQuery);
    }

	/**
	 * 分页查询用户消息列表
	 *
	 * @param userMessageQuery 用户消息
	 * @return 用户消息
	 */
	@Override
	public PageInfo<UserMessageDTO> find(UserMessageQuery userMessageQuery) {
        PageHelper.startPage(userMessageQuery.getPageNum(),userMessageQuery.getPageSize());
		List<UserMessageDTO> userMessageDTOList = userMessageDao.select(userMessageQuery);
		return new PageInfo<>(userMessageDTOList);
	}

    /**
     * 查询用户消息Map
     *
     * @param ids 编号集合
     * @return 用户消息Map
     */
    @Override
    public Map<Long, UserMessageDTO> findMapByIds(List<Long> ids) {
        Map<Long, UserMessageDTO> userMessageDTOMap = new HashMap<>();
        if (ids != null && !ids.isEmpty()){
            List<UserMessageDTO> userMessageDTOList =  userMessageDao.selectByIds(ids);
            for (UserMessageDTO userMessageDTO : userMessageDTOList) {
                    userMessageDTOMap.put(userMessageDTO.getId(),userMessageDTO);
            }
        }
        return userMessageDTOMap;
    }

    /**
     * 新增用户消息
     *
     * @param userMessage 用户消息
     * @return 结果
     */
    @Override
    public int create(UserMessage userMessage) {
        return userMessageDao.insert(userMessage);
    }

    /**
     * 修改用户消息
     *
     * @param userMessage 用户消息
     * @return 结果
     */
    @Override
    public int modifyById(UserMessage userMessage) {
        return userMessageDao.updateById(userMessage);
    }


    /**
     * 删除用户消息信息
     *
     * @param id 用户消息ID
     * @return 结果
     */
    @Override
    public int removeById(Long id) {
        return userMessageDao.deleteById(id);
    }

}
