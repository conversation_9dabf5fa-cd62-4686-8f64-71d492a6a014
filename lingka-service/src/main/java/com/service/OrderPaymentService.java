package com.service;

import java.util.List;
import java.util.Map;

import com.github.pagehelper.PageInfo;
import com.domain.OrderPayment;
import com.dto.OrderPaymentDTO;
import com.query.OrderPaymentQuery;

/**
 * 订单支付Service接口
 * 
 * <AUTHOR>
 * @date 2025-09-27
 */
public interface OrderPaymentService {
    /**
     * 查询订单支付
     * 
     * @param id 订单支付ID
     * @return 订单支付
     */
    OrderPaymentDTO findById(Long id);

    /**
     * 查询订单支付列表
     *
     * @param ids 编号集合
     * @return 订单支付集合
     */
    List<OrderPaymentDTO> findByIds(List<Long> ids);

    /**
     * 查询订单支付列表
     * 
     * @param orderPaymentQuery 订单支付
     * @return 订单支付集合
     */
    List<OrderPaymentDTO> findAll(OrderPaymentQuery orderPaymentQuery);

    /**
     * 查询最新的一条支付订单
     *
     * @param orderPaymentQuery 订单支付
     * @return 订单支付集合
     */
    OrderPaymentDTO findByMax(OrderPaymentQuery orderPaymentQuery);

	/**
	 *  分页查询订单支付列表
	 *
	 * @param orderPaymentQuery 订单支付
	 * @return 订单支付集合
	 */
	PageInfo<OrderPaymentDTO> find(OrderPaymentQuery orderPaymentQuery);

    /**
     * 查询订单支付Map
     *
     * @param ids 编号集合
     * @return 订单支付Map
     */
    Map<Long, OrderPaymentDTO> findMapByIds(List<Long> ids);

    /**
     * 新增订单支付
     * 
     * @param orderPayment 订单支付
     * @return 结果
     */
    int create(OrderPayment orderPayment);

    /**
     * 修改订单支付
     * 
     * @param orderPayment 订单支付
     * @return 结果
     */
    int modifyById(OrderPayment orderPayment);

    /**
     * 删除订单支付信息
     * 
     * @param id 订单支付id
     * @return 结果
     */
   int removeById(Long id);
}
