package com.dao;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.domain.OrderPayment;
import com.dto.OrderPaymentDTO;
import com.query.OrderPaymentQuery;

/**
 * 订单支付 接口
 * 
 * <AUTHOR>
 * @date 2025-09-27
 */

@Mapper
public interface OrderPaymentDao {
    /**
     * 查询订单支付
     * 
     * @param id 订单支付id
     * @return 订单支付
     */
    OrderPaymentDTO selectById(Long id);


    /**
     * 查询订单支付列表
     *
     * @param ids 编号集合
     * @return 订单支付集合
     */
    List<OrderPaymentDTO> selectByIds(List<Long> ids);


    /**
     * 查询订单支付列表
     * 
     * @param orderPaymentQuery 订单支付
     * @return 订单支付集合
     */
    List<OrderPaymentDTO> select(OrderPaymentQuery orderPaymentQuery);

    /**
     * 查询最新的一条订单
     *
     * @param orderPaymentQuery 订单支付
     * @return 订单支付集合
     */
    OrderPaymentDTO selectByMax(OrderPaymentQuery orderPaymentQuery);

    /**
     * 新增订单支付
     * 
     * @param orderPayment 订单支付
     * @return 结果
     */
    int insert(OrderPayment orderPayment);

    /**
     * 修改订单支付
     * 
     * @param orderPayment 订单支付
     * @return 结果
     */
    int updateById(OrderPayment orderPayment);

    /**
     * 删除订单支付
     * 
     * @param id 订单支付Id
     * @return 结果
     */
    int deleteById(Long id);

}
