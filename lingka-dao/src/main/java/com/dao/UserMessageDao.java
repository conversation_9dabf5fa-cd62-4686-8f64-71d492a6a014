package com.dao;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.domain.UserMessage;
import com.dto.UserMessageDTO;
import com.query.UserMessageQuery;

/**
 * 用户消息 接口
 * 
 * <AUTHOR>
 * @date 2025-09-21
 */

@Mapper
public interface UserMessageDao {
    /**
     * 查询用户消息
     * 
     * @param id 用户消息id
     * @return 用户消息
     */
    UserMessageDTO selectById(Long id);


    /**
     * 查询用户消息列表
     *
     * @param ids 编号集合
     * @return 用户消息集合
     */
    List<UserMessageDTO> selectByIds(List<Long> ids);


    /**
     * 查询用户消息列表
     * 
     * @param userMessageQuery 用户消息
     * @return 用户消息集合
     */
    List<UserMessageDTO> select(UserMessageQuery userMessageQuery);

    /**
     * 新增用户消息
     * 
     * @param userMessage 用户消息
     * @return 结果
     */
    int insert(UserMessage userMessage);

    /**
     * 修改用户消息
     * 
     * @param userMessage 用户消息
     * @return 结果
     */
    int updateById(UserMessage userMessage);

    /**
     * 删除用户消息
     * 
     * @param id 用户消息Id
     * @return 结果
     */
    int deleteById(Long id);

}
