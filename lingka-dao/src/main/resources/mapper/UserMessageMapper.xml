<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dao.UserMessageDao">
    
    <resultMap type="com.dto.UserMessageDTO" id="userMessageResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="shopId"    column="shop_id"    />
        <result property="title"    column="title"    />
        <result property="content"    column="content"    />
        <result property="businessCatalog"    column="business_catalog"    />
        <result property="businessId"    column="business_id"    />
        <result property="status"    column="status"    />
        <result property="modifyTime"    column="modify_time"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <select id="select" parameterType="com.query.UserMessageQuery" resultMap="userMessageResult">
        select * from tb_user_message
        <where>  
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="shopId != null "> and shop_id = #{shopId}</if>
            <if test="title != null  and title != ''"> and title = #{title}</if>
            <if test="content != null  and content != ''"> and content = #{content}</if>
            <if test="businessCatalog != null "> and business_catalog = #{businessCatalog}</if>
            <if test="businessId != null "> and business_id = #{businessId}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="modifyTime != null "> and modify_time = #{modifyTime}</if>
        </where>
        order by id desc
    </select>
    
    <select id="selectById" parameterType="java.lang.Long" resultMap="userMessageResult">
         select * from tb_user_message where id = #{id}
    </select>



    <select id="selectByIds" parameterType="java.util.List" resultMap="userMessageResult">
        select * from tb_user_message where id in
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        order by id desc
    </select>


    <insert id="insert" parameterType="com.domain.UserMessage" useGeneratedKeys="true" keyProperty="id">
        insert into tb_user_message
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="userId != null">user_id,</if>
            <if test="shopId != null">shop_id,</if>
            <if test="title != null">title,</if>
            <if test="content != null">content,</if>
            <if test="businessCatalog != null">business_catalog,</if>
            <if test="businessId != null">business_id,</if>
            <if test="status != null">status,</if>
            <if test="modifyTime != null">modify_time,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="userId != null">#{userId},</if>
            <if test="shopId != null">#{shopId},</if>
            <if test="title != null">#{title},</if>
            <if test="content != null">#{content},</if>
            <if test="businessCatalog != null">#{businessCatalog},</if>
            <if test="businessId != null">#{businessId},</if>
            <if test="status != null">#{status},</if>
            <if test="modifyTime != null">#{modifyTime},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateById" parameterType="com.domain.UserMessage">
        update tb_user_message
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="shopId != null">shop_id = #{shopId},</if>
            <if test="title != null">title = #{title},</if>
            <if test="content != null">content = #{content},</if>
            <if test="businessCatalog != null">business_catalog = #{businessCatalog},</if>
            <if test="businessId != null">business_id = #{businessId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteById" parameterType="java.lang.Long">
        delete from tb_user_message where id = #{id}
    </delete>


</mapper>