<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dao.OrderPaymentDao">
    
    <resultMap type="com.dto.OrderPaymentDTO" id="orderPaymentResult">
        <result property="id"    column="id"    />
        <result property="shopId"    column="shop_id"    />
        <result property="orderId"    column="order_id"    />
        <result property="paymentCode"    column="payment_code"    />
        <result property="prepayId"    column="prepay_id"    />
        <result property="requestHeader"    column="request_header"    />
        <result property="requestContent"    column="request_content"    />
        <result property="responseContent"    column="response_content"    />
        <result property="payStage"    column="pay_stage"    />
        <result property="expireTime"    column="expire_time"    />
        <result property="status"    column="status"    />
        <result property="modifyTime"    column="modify_time"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <select id="select" parameterType="com.query.OrderPaymentQuery" resultMap="orderPaymentResult">
        select * from tb_order_payment
        <where>  
            <if test="shopId != null "> and shop_id = #{shopId}</if>
            <if test="orderId != null "> and order_id = #{orderId}</if>
            <if test="paymentCode != null  and paymentCode != ''"> and payment_code = #{paymentCode}</if>
            <if test="prepayId != null  and prepayId != ''"> and prepay_id = #{prepayId}</if>
            <if test="requestHeader != null  and requestHeader != ''"> and request_header = #{requestHeader}</if>
            <if test="requestContent != null  and requestContent != ''"> and request_content = #{requestContent}</if>
            <if test="responseContent != null  and responseContent != ''"> and response_content = #{responseContent}</if>
            <if test="payStage != null  and payStage != ''"> and pay_stage = #{payStage}</if>
            <if test="expireTime != null "> and expire_time = #{expireTime}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="modifyTime != null "> and modify_time = #{modifyTime}</if>
        </where>
        order by id desc
    </select>

    <select id="selectByMax" parameterType="com.query.OrderPaymentQuery" resultMap="orderPaymentResult">
        select * from tb_order_payment
        <where>
            <if test="shopId != null "> and shop_id = #{shopId}</if>
            <if test="orderId != null "> and order_id = #{orderId}</if>
            <if test="paymentCode != null  and paymentCode != ''"> and payment_code = #{paymentCode}</if>
            <if test="prepayId != null  and prepayId != ''"> and prepay_id = #{prepayId}</if>
            <if test="requestHeader != null  and requestHeader != ''"> and request_header = #{requestHeader}</if>
            <if test="requestContent != null  and requestContent != ''"> and request_content = #{requestContent}</if>
            <if test="responseContent != null  and responseContent != ''"> and response_content = #{responseContent}</if>
            <if test="payStage != null  and payStage != ''"> and pay_stage = #{payStage}</if>
            <if test="expireTime != null "> and expire_time = #{expireTime}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="modifyTime != null "> and modify_time = #{modifyTime}</if>
        </where>
        order by id desc limit 0,1
    </select>
    
    <select id="selectById" parameterType="java.lang.Long" resultMap="orderPaymentResult">
         select * from tb_order_payment where id = #{id}
    </select>



    <select id="selectByIds" parameterType="java.util.List" resultMap="orderPaymentResult">
        select * from tb_order_payment where id in
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        order by id desc
    </select>


    <insert id="insert" parameterType="com.domain.OrderPayment" useGeneratedKeys="true" keyProperty="id">
        insert into tb_order_payment
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="shopId != null">shop_id,</if>
            <if test="orderId != null">order_id,</if>
            <if test="paymentCode != null">payment_code,</if>
            <if test="prepayId != null">prepay_id,</if>
            <if test="requestHeader != null">request_header,</if>
            <if test="requestContent != null">request_content,</if>
            <if test="responseContent != null">response_content,</if>
            <if test="payStage != null">pay_stage,</if>
            <if test="expireTime != null">expire_time,</if>
            <if test="status != null">status,</if>
            <if test="modifyTime != null">modify_time,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="shopId != null">#{shopId},</if>
            <if test="orderId != null">#{orderId},</if>
            <if test="paymentCode != null">#{paymentCode},</if>
            <if test="prepayId != null">#{prepayId},</if>
            <if test="requestHeader != null">#{requestHeader},</if>
            <if test="requestContent != null">#{requestContent},</if>
            <if test="responseContent != null">#{responseContent},</if>
            <if test="payStage != null">#{payStage},</if>
            <if test="expireTime != null">#{expireTime},</if>
            <if test="status != null">#{status},</if>
            <if test="modifyTime != null">#{modifyTime},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateById" parameterType="com.domain.OrderPayment">
        update tb_order_payment
        <trim prefix="SET" suffixOverrides=",">
            <if test="shopId != null">shop_id = #{shopId},</if>
            <if test="orderId != null">order_id = #{orderId},</if>
            <if test="paymentCode != null">payment_code = #{paymentCode},</if>
            <if test="prepayId != null">prepay_id = #{prepayId},</if>
            <if test="requestHeader != null">request_header = #{requestHeader},</if>
            <if test="requestContent != null">request_content = #{requestContent},</if>
            <if test="responseContent != null">response_content = #{responseContent},</if>
            <if test="payStage != null">pay_stage = #{payStage},</if>
            <if test="expireTime != null">expire_time = #{expireTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteById" parameterType="java.lang.Long">
        delete from tb_order_payment where id = #{id}
    </delete>


</mapper>