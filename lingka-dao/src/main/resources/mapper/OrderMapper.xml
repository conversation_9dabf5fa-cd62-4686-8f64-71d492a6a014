<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dao.OrderDao">
    
    <resultMap type="com.dto.OrderDTO" id="orderResult">
        <result property="id"    column="id"    />
        <result property="shopId"    column="shop_id"    />
        <result property="userId"    column="user_id"    />
        <result property="tableId"    column="table_id"    />
        <result property="code"    column="code"    />
        <result property="type"    column="type"    />
        <result property="originalAmount"    column="original_amount"    />
        <result property="finalAmount"    column="final_amount"    />
        <result property="reducedAmount"    column="reduced_amount"    />
        <result property="orderStage"    column="order_stage"    />
        <result property="subOrderStage"    column="sub_order_stage"    />
        <result property="payStage"    column="pay_stage"    />
        <result property="orderTime"    column="order_time"    />
        <result property="payTime"    column="pay_time"    />
        <result property="userComment"    column="user_comment"    />
        <result property="userStatus"    column="user_status"    />
        <result property="status"    column="status"    />
        <result property="modifyTime"    column="modify_time"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <select id="select" parameterType="com.query.OrderQuery" resultMap="orderResult">
        select * from tb_order
        <where>  
            <if test="shopId != null "> and shop_id = #{shopId}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="tableId != null "> and table_id = #{tableId}</if>
            <if test="code != null  and code != ''"> and code = #{code}</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="orderStage != null  and orderStage != ''"> and order_stage = #{orderStage}</if>
            <if test="orderStages != null and orderStages.size > 0">
                and order_stage in
                <foreach collection="orderStages" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="subOrderStage != null  and subOrderStage != ''"> and sub_order_stage = #{subOrderStage}</if>
            <if test="payStage != null  and payStage != ''"> and pay_stage = #{payStage}</if>
            <if test="orderTime != null "> and order_time = #{orderTime}</if>
            <if test="minOrderTime != null"> and order_time <![CDATA[ >= ]]> #{minOrderTime}</if>
            <if test="maxOrderTime != null"> and order_time <![CDATA[ < ]]> #{maxOrderTime}</if>
            <if test="payTime != null "> and pay_time = #{payTime}</if>
            <if test="userComment != null  and userComment != ''"> and user_comment = #{userComment}</if>
            <if test="userStatus != null  and userStatus != ''"> and user_status = #{userStatus}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="modifyTime != null "> and modify_time = #{modifyTime}</if>
        </where>
        order by id desc
    </select>
    
    <select id="selectById" parameterType="java.lang.Long" resultMap="orderResult">
         select * from tb_order where id = #{id}
    </select>

    <select id="selectByIds" parameterType="java.util.List" resultMap="orderResult">
        select * from tb_order where id in
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        order by id desc
    </select>


    <insert id="insert" parameterType="com.domain.Order" useGeneratedKeys="true" keyProperty="id">
        insert into tb_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="shopId != null">shop_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="tableId != null">table_id,</if>
            <if test="code != null">code,</if>
            <if test="type != null">type,</if>
            <if test="originalAmount != null">original_amount,</if>
            <if test="finalAmount != null">final_amount,</if>
            <if test="reducedAmount != null">reduced_amount,</if>
            <if test="orderStage != null">order_stage,</if>
            <if test="subOrderStage != null">sub_order_stage,</if>
            <if test="payStage != null">pay_stage,</if>
            <if test="orderTime != null">order_time,</if>
            <if test="payTime != null">pay_time,</if>
            <if test="userComment != null">user_comment,</if>
            <if test="userStatus != null">user_status,</if>
            <if test="status != null">status,</if>
            <if test="modifyTime != null">modify_time,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="shopId != null">#{shopId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="tableId != null">#{tableId},</if>
            <if test="code != null">#{code},</if>
            <if test="type != null">#{type},</if>
            <if test="originalAmount != null">#{originalAmount},</if>
            <if test="finalAmount != null">#{finalAmount},</if>
            <if test="reducedAmount != null">#{reducedAmount},</if>
            <if test="orderStage != null">#{orderStage},</if>
            <if test="subOrderStage != null">#{subOrderStage},</if>
            <if test="payStage != null">#{payStage},</if>
            <if test="orderTime != null">#{orderTime},</if>
            <if test="payTime != null">#{payTime},</if>
            <if test="userComment != null">#{userComment},</if>
            <if test="userStatus != null">#{userStatus},</if>
            <if test="status != null">#{status},</if>
            <if test="modifyTime != null">#{modifyTime},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateById" parameterType="com.domain.Order">
        update tb_order
        <trim prefix="SET" suffixOverrides=",">
            <if test="shopId != null">shop_id = #{shopId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="tableId != null">table_id = #{tableId},</if>
            <if test="code != null">code = #{code},</if>
            <if test="type != null">type = #{type},</if>
            <if test="finalAmount != null">final_amount = #{finalAmount},</if>
            <if test="reducedAmount != null">reduced_amount = #{reducedAmount},</if>
            <if test="originalAmount != null">original_amount = #{originalAmount},</if>
            <if test="orderStage != null">order_stage = #{orderStage},</if>
            <if test="payStage != null">pay_stage = #{payStage},</if>
            <if test="subOrderStage != null">sub_order_stage = #{subOrderStage},</if>
            <if test="orderTime != null">order_time = #{orderTime},</if>
            <if test="payTime != null">pay_time = #{payTime},</if>
            <if test="userComment != null">user_comment = #{userComment},</if>
            <if test="userStatus != null">user_status = #{userStatus},</if>
            <if test="status != null">status = #{status},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteById" parameterType="java.lang.Long">
        delete from tb_order where id = #{id}
    </delete>


</mapper>