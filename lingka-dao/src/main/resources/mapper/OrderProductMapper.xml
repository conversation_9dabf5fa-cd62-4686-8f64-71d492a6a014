<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dao.OrderProductDao">
    
    <resultMap type="com.dto.OrderProductDTO" id="orderProductResult">
        <result property="id"    column="id"    />
        <result property="orderId"    column="order_id"    />
        <result property="productId"    column="product_id"    />
        <result property="productName"    column="product_name"    />
        <result property="productCover"    column="product_cover"    />
        <result property="number"    column="number"    />
        <result property="originalAmount"    column="original_amount"    />
        <result property="finalAmount"    column="final_amount"    />
        <result property="reducedAmount"    column="reduced_amount"    />
        <result property="productSkuName"    column="product_sku_name"    />
        <result property="productOptionName"    column="product_option_name"    />
        <result property="productIngredientName"    column="product_ingredient_name"    />
        <result property="status"    column="status"    />
        <result property="modifyTime"    column="modify_time"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <select id="select" parameterType="com.query.OrderProductQuery" resultMap="orderProductResult">
        select * from tb_order_product
        <where>  
            <if test="orderId != null "> and order_id = #{orderId}</if>
            <if test="orderIds != null and orderIds.size > 0">
                and order_id in
                <foreach collection="orderIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="productId != null "> and product_id = #{productId}</if>
            <if test="productName != null "> and product_name like concat('%', #{productName}, '%')</if>
            <if test="productCover != null "> and product_cover = #{productCover}</if>
            <if test="number != null "> and number = #{number}</if>
            <if test="productSkuName != null  and productSkuName != ''"> and product_sku_name like concat('%', #{productSkuName}, '%')</if>
            <if test="productOptionName != null  and productOptionName != ''"> and product_option_name like concat('%', #{productOptionName}, '%')</if>
            <if test="productIngredientName != null  and productIngredientName != ''"> and product_ingredient_name like concat('%', #{productIngredientName}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="modifyTime != null "> and modify_time = #{modifyTime}</if>
        </where>
        order by id desc
    </select>
    
    <select id="selectById" parameterType="java.lang.Long" resultMap="orderProductResult">
         select * from tb_order_product where id = #{id}
    </select>

    <select id="selectByIds" parameterType="java.util.List" resultMap="orderProductResult">
        select * from tb_order_product where id in
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        order by id desc
    </select>

    <insert id="insert" parameterType="com.domain.OrderProduct" useGeneratedKeys="true" keyProperty="id">
        insert into tb_order_product
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="orderId != null">order_id,</if>
            <if test="productId != null">product_id,</if>
            <if test="productName != null">product_name,</if>
            <if test="productCover != null">product_cover,</if>
            <if test="number != null">number,</if>
            <if test="originalAmount != null">original_amount,</if>
            <if test="finalAmount != null">final_amount,</if>
            <if test="reducedAmount != null">reduced_amount,</if>
            <if test="productSkuName != null">product_sku_name,</if>
            <if test="productOptionName != null">product_option_name,</if>
            <if test="productIngredientName != null">product_ingredient_name,</if>
            <if test="status != null">status,</if>
            <if test="modifyTime != null">modify_time,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="orderId != null">#{orderId},</if>
            <if test="productId != null">#{productId},</if>
            <if test="productName != null">#{productName},</if>
            <if test="productCover != null">#{productCover},</if>
            <if test="number != null">#{number},</if>
            <if test="originalAmount != null">#{originalAmount},</if>
            <if test="finalAmount != null">#{finalAmount},</if>
            <if test="reducedAmount != null">#{reducedAmount},</if>
            <if test="productSkuName != null">#{productSkuName},</if>
            <if test="productOptionName != null">#{productOptionName},</if>
            <if test="productIngredientName != null">#{productIngredientName},</if>
            <if test="status != null">#{status},</if>
            <if test="modifyTime != null">#{modifyTime},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateById" parameterType="com.domain.OrderProduct">
        update tb_order_product
        <trim prefix="SET" suffixOverrides=",">
            <if test="orderId != null">order_id = #{orderId},</if>
            <if test="productId != null">product_id = #{productId},</if>
            <if test="productName != null">product_name = #{productName},</if>
            <if test="productCover != null">product_cover = #{productCover},</if>
            <if test="number != null">number = #{number},</if>
            <if test="finalAmount != null">final_amount = #{finalAmount},</if>
            <if test="reducedAmount != null">reduced_amount = #{reducedAmount},</if>
            <if test="originalAmount != null">original_amount = #{originalAmount},</if>
            <if test="productSkuName != null">product_sku_name = #{productSkuName},</if>
            <if test="productOptionName != null">product_option_name = #{productOptionName},</if>
            <if test="productIngredientName != null">product_ingredient_name = #{productIngredientName},</if>
            <if test="status != null">status = #{status},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteById" parameterType="java.lang.Long">
        delete from tb_order_product where id = #{id}
    </delete>


</mapper>