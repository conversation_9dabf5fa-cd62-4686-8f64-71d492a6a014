<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dao.OrderTicketDetailDao">
    
    <resultMap type="com.dto.OrderTicketDetailDTO" id="orderTicketDetailResult">
        <result property="id"    column="id"    />
        <result property="orderId"    column="order_id"    />
        <result property="ticketId"    column="ticket_id"    />
        <result property="ticketName"    column="ticket_name"    />
        <result property="number"    column="number"    />
        <result property="finalAmount"    column="final_amount"    />
        <result property="status"    column="status"    />
        <result property="modifyTime"    column="modify_time"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <select id="select" parameterType="com.query.OrderTicketDetailQuery" resultMap="orderTicketDetailResult">
        select * from tb_order_ticket_detail
        <where>  
            <if test="orderId != null "> and order_id = #{orderId}</if>
            <if test="orderIds != null and orderIds.size > 0">
                and order_id in
                <foreach collection="orderIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="ticketId != null "> and ticket_id = #{ticketId}</if>
            <if test="ticketName != null  and ticketName != ''"> and ticket_name like concat('%', #{ticketName}, '%')</if>
            <if test="number != null "> and number = #{number}</if>
            <if test="finalAmount != null "> and final_amount = #{finalAmount}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="modifyTime != null "> and modify_time = #{modifyTime}</if>
        </where>
        order by id desc
    </select>
    
    <select id="selectById" parameterType="java.lang.Long" resultMap="orderTicketDetailResult">
         select * from tb_order_ticket_detail where id = #{id}
    </select>



    <select id="selectByIds" parameterType="java.util.List" resultMap="orderTicketDetailResult">
        select * from tb_order_ticket_detail where id in
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        order by id desc
    </select>


    <insert id="insert" parameterType="com.domain.OrderTicketDetail" useGeneratedKeys="true" keyProperty="id">
        insert into tb_order_ticket_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="orderId != null">order_id,</if>
            <if test="ticketId != null">ticket_id,</if>
            <if test="ticketName != null">ticket_name,</if>
            <if test="number != null">number,</if>
            <if test="finalAmount != null">final_amount,</if>
            <if test="status != null">status,</if>
            <if test="modifyTime != null">modify_time,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="orderId != null">#{orderId},</if>
            <if test="ticketId != null">#{ticketId},</if>
            <if test="ticketName != null">#{ticketName},</if>
            <if test="number != null">#{number},</if>
            <if test="finalAmount != null">#{finalAmount},</if>
            <if test="status != null">#{status},</if>
            <if test="modifyTime != null">#{modifyTime},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateById" parameterType="com.domain.OrderTicketDetail">
        update tb_order_ticket_detail
        <trim prefix="SET" suffixOverrides=",">
            <if test="orderId != null">order_id = #{orderId},</if>
            <if test="ticketId != null">ticket_id = #{ticketId},</if>
            <if test="ticketName != null">ticket_name = #{ticketName},</if>
            <if test="number != null">number = #{number},</if>
            <if test="finalAmount != null">final_amount = #{finalAmount},</if>
            <if test="status != null">status = #{status},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteById" parameterType="java.lang.Long">
        delete from tb_order_ticket_detail where id = #{id}
    </delete>


</mapper>