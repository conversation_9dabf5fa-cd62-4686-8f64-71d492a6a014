package com.api.service;

import com.api.websocket.WebSocketSessionManager;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class OrderNotificationService {
    
    @Autowired
    private WebSocketSessionManager sessionManager;
    
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    /**
     * 通知指定用户有新订单需要接单
     * @param userId 用户ID
     * @param orderId 订单ID
     * @param orderInfo 订单信息
     */
    public void notifyNewOrder(Long userId, Long orderId, String orderInfo) {
        try {
            Map<String, Object> notification = new HashMap<>();
            notification.put("type", "NEW_ORDER");
            notification.put("orderId", orderId);
            notification.put("message", "有新订单需要接单");
            notification.put("orderInfo", orderInfo);
            notification.put("timestamp", System.currentTimeMillis());
            
            String message = objectMapper.writeValueAsString(notification);
            sessionManager.sendMessageToUser(userId, message);
            
            System.out.println("已通知用户 " + userId + " 新订单：" + orderId);
        } catch (Exception e) {
            System.err.println("发送新订单通知失败：" + e.getMessage());
        }
    }
    
    /**
     * 通知多个用户有新订单
     * @param userIds 用户ID列表
     * @param orderId 订单ID
     * @param orderInfo 订单信息
     */
    public void notifyNewOrderToMultipleUsers(List<Long> userIds, Long orderId, String orderInfo) {
        userIds.forEach(userId -> notifyNewOrder(userId, orderId, orderInfo));
    }
    
    /**
     * 广播新订单通知给所有在线用户
     * @param orderId 订单ID
     * @param orderInfo 订单信息
     */
    public void broadcastNewOrder(Long orderId, String orderInfo) {
        try {
            Map<String, Object> notification = new HashMap<>();
            notification.put("type", "NEW_ORDER_BROADCAST");
            notification.put("orderId", orderId);
            notification.put("message", "有新订单需要接单");
            notification.put("orderInfo", orderInfo);
            notification.put("timestamp", System.currentTimeMillis());
            
            String message = objectMapper.writeValueAsString(notification);
            sessionManager.broadcastMessage(message);
            
            System.out.println("已广播新订单通知：" + orderId);
        } catch (Exception e) {
            System.err.println("广播新订单通知失败：" + e.getMessage());
        }
    }
    
    /**
     * 通知订单状态变更
     * @param userId 用户ID
     * @param orderId 订单ID
     * @param status 新状态
     * @param message 状态变更消息
     */
    public void notifyOrderStatusChange(Long userId, Long orderId, String status, String message) {
        try {
            Map<String, Object> notification = new HashMap<>();
            notification.put("type", "ORDER_STATUS_CHANGE");
            notification.put("orderId", orderId);
            notification.put("status", status);
            notification.put("message", message);
            notification.put("timestamp", System.currentTimeMillis());
            
            String notificationMessage = objectMapper.writeValueAsString(notification);
            sessionManager.sendMessageToUser(userId, notificationMessage);
            
            System.out.println("已通知用户 " + userId + " 订单 " + orderId + " 状态变更：" + status);
        } catch (Exception e) {
            System.err.println("发送订单状态变更通知失败：" + e.getMessage());
        }
    }
}
