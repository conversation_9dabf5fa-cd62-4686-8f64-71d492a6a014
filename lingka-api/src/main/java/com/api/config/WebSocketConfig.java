package com.api.config;

import org.springframework.boot.SpringBootConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.web.socket.config.annotation.WebSocketConfigurer;
import org.springframework.web.socket.config.annotation.WebSocketHandlerRegistry;

import com.api.websocket.AcceptOrderSocketHandler;

@SpringBootConfiguration
public class WebSocketConfig implements WebSocketConfigurer {

    @Bean
    public AcceptOrderSocketHandler acceptOrderSocketHandler() {
        return new AcceptOrderSocketHandler();
    }

    @Bean
    public AuthHandshakeInterceptor authHandshakeInterceptor() {
        return new AuthHandshakeInterceptor();
    }

    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
        registry.addHandler(acceptOrderSocketHandler(), "/ws/chat")
                .addInterceptors(authHandshakeInterceptor())
                .setAllowedOrigins("*");
    }
}
