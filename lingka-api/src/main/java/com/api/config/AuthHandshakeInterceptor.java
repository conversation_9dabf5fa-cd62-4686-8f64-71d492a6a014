package com.api.config;

import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.socket.WebSocketHandler;
import org.springframework.web.socket.server.HandshakeInterceptor;

import com.api.constant.App;
import com.common.bean.JsonWebToken;
import com.common.util.EncryptUtil;
import com.common.util.StringUtil;

public class AuthHandshakeInterceptor implements HandshakeInterceptor {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Override
    public boolean beforeHandshake(ServerHttpRequest request, ServerHttpResponse response, WebSocketHandler wsHandler, Map<String, Object> attributes) throws Exception {
        // 从token中获取
        String token = request.getHeaders().getFirst(App.WEBSOCKET_HEADER_APP_TOKEN);
        if (!StringUtil.isEmpty(token)) {
            JsonWebToken jsonWebToken = EncryptUtil.parseJwt(token);
            logger.info("token id : {}",jsonWebToken.getId());
            logger.info("token type : {}",jsonWebToken.getType());
            logger.info("user id : {}",jsonWebToken.getUserId());
            attributes.put("userId", jsonWebToken.getUserId());
            attributes.put("type", jsonWebToken.getType());
        }
        return true;
    }

    @Override
    public void afterHandshake(ServerHttpRequest request, ServerHttpResponse response, WebSocketHandler wsHandler, Exception exception) {

    }
}
