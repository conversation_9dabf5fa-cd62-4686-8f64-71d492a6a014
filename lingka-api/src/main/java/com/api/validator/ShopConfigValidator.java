package com.api.validator;

public class ShopConfigValidator extends Validator {

	public ShopConfigValidator onId(Long str){
		if(this.isEmpty(str)){
			this.addAttribute(errors, "请输入管理员ID");
			this.result = false;
		}
		return this;
	}

	public ShopConfigValidator onShopId(Long str){
		if(this.isEmpty(str)){
			this.addAttribute(errors, "请输入店铺ID");
			this.result = false;
		}
		return this;
	}

	public ShopConfigValidator onTicketAutoAuditSwitch(String str){
		if(this.isEmpty(str)){
			this.addAttribute(errors, "请选择自动报名审核开关");
			this.result = false;
		}
		return this;
	}




}
