package com.api.validator;

import java.util.List;

public class UserMessageValidator extends Validator {

    public UserMessageValidator onId(Long id) {
        if (this.isEmpty(id)) {
            this.addAttribute(errors, "请输入ID");
            this.result = false;
        }
        return this;
    }

    public UserMessageValidator onReadStage(String id) {
        if (this.isEmpty(id)) {
            this.addAttribute(errors, "缺少已/未读状态");
            this.result = false;
        }
        return this;
    }

    public UserMessageValidator onIds(List<Long> ids) {
        if (this.isEmpty(ids) || ids.isEmpty()) {
            this.addAttribute(errors, "请输入IDS");
            this.result = false;
        }
        return this;
    }


}
