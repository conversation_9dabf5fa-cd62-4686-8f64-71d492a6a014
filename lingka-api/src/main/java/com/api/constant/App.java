package com.api.constant;

import java.util.Calendar;

public class App {
    public static String ID = null;
    public static String NAME = null;
    public static String DOMAIN = null;
    public static final String TOKEN = "38659215534F141B5496BDF477D909A0";
    public static final String NOTIFY = "notify";
    public static final String CHARSET = "UTF-8";
    public static final String APPLICATION_JSON_UTF8_VALUE = "application/json;charset=UTF-8";
    public static final String TITLE = "title";
    public static final String COMMA = ",";
    public static final String DOT = ".";
    public static final String SEMICOLON = ";";
    public static final String PLUS = "+";
    public static final String MINUS = "-";
    public static final String MULTI = "*";
    public static final String PAUSE = "、";

    public static final String HTTP_HEADER_APP_TOKEN = "X-App-Token";
    public static final String HTTP_HEADER_APP_VERSION = "X-App-Version";
    public static final String HTTP_HEADER_APP_PLATFORM = "X-App-Platform";
    public static final String HTTP_HEADER_APP_AGENT = "X-App-Agent";

    public static final String WEBSOCKET_HEADER_APP_TOKEN = "X-App-Token";
    public static final String WEBSOCKET_HEADER_APP_VERSION = "X-App-Version";
    public static final String WEBSOCKET_HEADER_APP_PLATFORM = "X-App-Platform";
    public static final String WEBSOCKET_HEADER_APP_AGENT = "X-App-Agent";

    public static final int TOKEN_EXPIRE_UNIT = Calendar.MONTH;
    public static final int TOKEN_EXPIRE_NUMBER = 12;

    public static final boolean PRODUCTION = true;

    public static final int CAPTCHA_LIMIT_MAX = 3;
    public static final int CAPTCHA_LIMIT_EXPIRE_TIME = 24;

    public static final int MEDIUMTEXT_MAX_BYTE_SIZE = 16777215;
    public static String FILE_LOCATION = null;


    public static String IMAGE_VIEW = "/v1/file/image?url=";

    public static final String EMPTY_MESSAGE = "暂无信息";

    public static final int ROOT = 0;
    /**
     *  默认密码   000000 MD5加密后
     */
    public static final String PASSWORD = "670B14728AD9902AECBA32E22FA4F6BD";


}
