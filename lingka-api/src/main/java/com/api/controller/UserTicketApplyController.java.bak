package com.api.controller;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.locks.Lock;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.api.config.Token;
import com.api.validator.UserTicketApplyValidator;
import com.common.bean.Response;
import com.common.bean.UserMessagePayload;
import com.common.constant.App;
import com.common.constant.BusinessCatalog;
import com.common.constant.CacheKey;
import com.common.constant.DataStatus;
import com.common.constant.Gender;
import com.common.constant.TokenType;
import com.common.constant.UserTicketApplyStage;
import com.common.constant.UserTicketCatalog;
import com.common.constant.UserTicketStage;
import com.common.util.StringUtil;
import com.domain.UserTicket;
import com.domain.UserTicketApply;
import com.dto.OrderTicketDetailDTO;
import com.dto.ShopConfigDTO;
import com.dto.TicketDTO;
import com.dto.UserDTO;
import com.dto.UserTicketApplyDTO;
import com.github.pagehelper.PageInfo;
import com.query.OrderTicketDetailQuery;
import com.query.UserTicketApplyQuery;
import com.service.OrderTicketDetailService;
import com.service.ShopConfigService;
import com.service.ShopService;
import com.service.TicketService;
import com.service.UserService;
import com.service.UserTicketApplyService;

/**
 * 报名审核模块
 *
 * <AUTHOR>
 * @date 2025-09-14
 */
@RestController
public class UserTicketApplyController extends BaseController {

    @Autowired
    private UserTicketApplyService userTicketApplyService;
    @Autowired
    private OrderTicketDetailService orderTicketDetailService;
    @Autowired
    private TicketService ticketService;
    @Autowired
    private UserService userService;
    @Autowired
    private RedisTemplate<String,String> redisTemplate;
    @Autowired
    private ShopConfigService shopConfigService;


    /**
     * 分页查询用户门票报名列表
     */
    @RequestMapping(value = "/v1/user/ticket/apply/query", method = RequestMethod.POST)
    @Token(tokenType = TokenType.C)
    public Response<PageInfo<UserTicketApplyDTO>> query(@RequestBody UserTicketApplyQuery userTicketApplyQuery) {
        userTicketApplyQuery.setStatus(DataStatus.Y.getCode());
        PageInfo<UserTicketApplyDTO> pageInfo = userTicketApplyService.find(userTicketApplyQuery);
        if (pageInfo.getList() == null || pageInfo.getList().isEmpty()) {
            return new Response<>(pageInfo);
        }
        List<Long> orderIds = new ArrayList<>();
        List<Long> userIds = new ArrayList<>();
        for (UserTicketApplyDTO userTicketApplyDTO : pageInfo.getList()) {
            orderIds.add(userTicketApplyDTO.getOrderId());
            userIds.add(userTicketApplyDTO.getUserId());
        }
        // 查询订单的门票信息
        OrderTicketDetailQuery orderTicketDetailQuery = new OrderTicketDetailQuery();
        orderTicketDetailQuery.setOrderIds(orderIds);
        orderTicketDetailQuery.setStatus(DataStatus.Y.getCode());
        List<OrderTicketDetailDTO> orderTicketDetailDTOs = orderTicketDetailService.findAll(orderTicketDetailQuery);
        Map<Long, List<OrderTicketDetailDTO>> orderIdOrderTicketDetailDTOMap = new HashMap<>();
        // 查询门票信息
        List<Long> ticketIds = new ArrayList<>();
        for (OrderTicketDetailDTO orderTicketDetailDTO : orderTicketDetailDTOs) {
            ticketIds.add(orderTicketDetailDTO.getTicketId());
            if (!orderIdOrderTicketDetailDTOMap.containsKey(orderTicketDetailDTO.getOrderId())) {
                orderIdOrderTicketDetailDTOMap.put(orderTicketDetailDTO.getOrderId(), new ArrayList<>());
            }
            orderIdOrderTicketDetailDTOMap.get(orderTicketDetailDTO.getOrderId()).add(orderTicketDetailDTO);
        }
        Map<Long, TicketDTO> ticketDTOMap = ticketService.findMapByIds(ticketIds);
        Map<Long, UserDTO> userDTOMap = userService.findMapByIds(userIds);
        for (UserTicketApplyDTO userTicketApplyDTO : pageInfo.getList()) {
            if (orderIdOrderTicketDetailDTOMap.containsKey(userTicketApplyDTO.getOrderId())) {
                List<OrderTicketDetailDTO> orderTicketDetailDTOList = orderIdOrderTicketDetailDTOMap.get(userTicketApplyDTO.getOrderId());
                for (OrderTicketDetailDTO orderTicketDetailDTO : orderTicketDetailDTOList) {
                    orderTicketDetailDTO.setTicketName(ticketDTOMap.get(orderTicketDetailDTO.getTicketId()).getName());
                }
                userTicketApplyDTO.setOrderTicketDetailList(orderTicketDetailDTOList);
            }
            // 用户信息
            if (userDTOMap.containsKey(userTicketApplyDTO.getUserId())) {
                UserDTO userDTO = userDTOMap.get(userTicketApplyDTO.getUserId());
                userDTO.setMobile(this.getMobile(userDTO.getMobile()));
                userDTO.setOpenid(null);
                userDTO.setGenderName(Gender.getName(userDTO.getGender()));
                userTicketApplyDTO.setUser(userDTO);
            }
            userTicketApplyDTO.setPhotoList(StringUtil.split(userTicketApplyDTO.getPhoto(), App.COMMA));
            userTicketApplyDTO.setStageName(UserTicketApplyStage.getName(userTicketApplyDTO.getStage()));
        }
        return new Response<>(pageInfo);
    }


    /**
     * 审核通过用户门票报名
     */
    @RequestMapping(value = "/v1/user/ticket/apply/pass", method = RequestMethod.POST)
    @Token(tokenType = {TokenType.C})
    public Response<?> passApply(@RequestBody UserTicketApply request) {
        UserTicketApplyValidator validator = new UserTicketApplyValidator();
        if (!validator.onId(request.getId()).result()){
            return new Response<>(ERROR,validator.getErrorMessage());
        }
        Lock lock = this.getLock(redisTemplate, CacheKey.LOCK_USER_TICKET_APPLY, request.getId().toString());
        if (!lock.tryLock()){
            return new Response<>(ERROR,"请勿重复提交");
        }
        try {
            UserTicketApplyDTO userTicketApplyDTO = userTicketApplyService.findById(request.getId());
            if (userTicketApplyDTO == null || !DataStatus.Y.getCode().equals(userTicketApplyDTO.getStatus())) {
                return new Response<>(ERROR, "未查询到该门票报名,无法通过审核");
            }
            if (!UserTicketApplyStage.PENDING.getCode().equals(userTicketApplyDTO.getStage())) {
                return new Response<>(ERROR, "该门票报名已审核,无法再次审核");
            }
            // 查询这个订单购买了几张门票
            OrderTicketDetailQuery orderTicketDetailQuery = new OrderTicketDetailQuery();
            orderTicketDetailQuery.setOrderId(userTicketApplyDTO.getOrderId());
            orderTicketDetailQuery.setStatus(DataStatus.Y.getCode());
            List<OrderTicketDetailDTO> orderTicketDetailDTOS = orderTicketDetailService.findAll(orderTicketDetailQuery);
            if (orderTicketDetailDTOS == null || orderTicketDetailDTOS.isEmpty()) {
                return new Response<>(ERROR, "未查询到该订单的门票信息,无法再次审核");
            }
            // 审核通过，并按照订单生成门票
            UserTicketApply userTicketApply = new UserTicketApply();
            userTicketApply.setId(request.getId());
            userTicketApply.setStage(UserTicketApplyStage.PASS.getCode());
            userTicketApply.setComment(request.getComment());
            userTicketApply.setModifyTime(this.getServerTime());
            List<UserTicket> userTickets = new ArrayList<>();
            for (OrderTicketDetailDTO orderTicketDetailDTO : orderTicketDetailDTOS) {
                for (int i = 0; i < orderTicketDetailDTO.getNumber(); i++) {
                    UserTicket userTicket = new UserTicket();
                    userTicket.setUserId(userTicketApplyDTO.getUserId());
                    userTicket.setShopId(userTicketApplyDTO.getShopId());
                    userTicket.setOrderId(userTicketApplyDTO.getOrderId());
                    userTicket.setTicketId(orderTicketDetailDTO.getTicketId());
                    userTicket.setApplyTime(userTicketApplyDTO.getApplyTime());
                    userTicket.setStage(UserTicketStage.VERIFIED.getCode());
                    userTicket.setCatalog(UserTicketCatalog.SELF_BUY.getCode());
                    userTicket.setStatus(DataStatus.Y.getCode());
                    userTicket.setModifyTime(this.getServerTime());
                    userTicket.setCreateTime(this.getServerTime());
                    userTickets.add(userTicket);
                }
            }
            userTicketApplyService.pass(userTicketApply,userTickets);
            // 发送消息给用户
            ShopConfigDTO shopConfigDTO = shopConfigService.findByShopId(userTicketApplyDTO.getShopId());
            String shopName = "";
            if (shopConfigDTO != null) {
                shopName = shopConfigDTO.getName();
            }
            UserMessagePayload userMessagePayload = new UserMessagePayload();
            userMessagePayload.setUserId(userTicketApplyDTO.getUserId());
            userMessagePayload.setShopId(userTicketApplyDTO.getShopId());
            userMessagePayload.setTitle("报名审核通过");
            userMessagePayload.setContent(shopName + ",您的门票报名已通过审核");
            userMessagePayload.setBusinessCatalog(BusinessCatalog.USER_TICKET_APPLY.getCode());
            userMessagePayload.setBusinessId(userTicketApplyDTO.getId());
            userMessagePayload.setBusinessId(userTicketApplyDTO.getId());
            redisTemplate.opsForList().leftPush(CacheKey.USER_MESSAGE_QUEUE, this.getJSON(userMessagePayload));
            return new Response<>(OK, SUCCESS);
        }catch (Exception e){
            logger.error(e.getMessage(),e);
            return new Response<>(ERROR, FAILURE);
        }finally {
            lock.unlock();
        }
    }


    /**
     * 驳回用户门票报名
     */
    @RequestMapping(value = "/v1/user/ticket/apply/reject", method = RequestMethod.POST)
    @Token(tokenType = {TokenType.C})
    public Response<?> rejectApply(@RequestBody UserTicketApply request) {
        UserTicketApplyValidator validator = new UserTicketApplyValidator();
        if (!validator.onId(request.getId()).onComment(request.getComment()).result()){
            return new Response<>(ERROR,validator.getErrorMessage());
        }
        Lock lock = this.getLock(redisTemplate, CacheKey.LOCK_USER_TICKET_APPLY, request.getId().toString());
        if (!lock.tryLock()){
            return new Response<>(ERROR,"请勿重复提交");
        }
        try {
            UserTicketApplyDTO userTicketApplyDTO = userTicketApplyService.findById(request.getId());
            if (userTicketApplyDTO == null || !DataStatus.Y.getCode().equals(userTicketApplyDTO.getStatus())) {
                return new Response<>(ERROR, "未查询到该门票报名,无法驳回");
            }
            if (!UserTicketApplyStage.PENDING.getCode().equals(userTicketApplyDTO.getStage())) {
                return new Response<>(ERROR, "该门票报名已审核,无法驳回");
            }
            // 查询这个订单购买了几张门票
            OrderTicketDetailQuery orderTicketDetailQuery = new OrderTicketDetailQuery();
            orderTicketDetailQuery.setOrderId(userTicketApplyDTO.getOrderId());
            orderTicketDetailQuery.setStatus(DataStatus.Y.getCode());
            List<OrderTicketDetailDTO> orderTicketDetailDTOS = orderTicketDetailService.findAll(orderTicketDetailQuery);
            if (orderTicketDetailDTOS == null || orderTicketDetailDTOS.isEmpty()) {
                return new Response<>(ERROR, "未查询到该订单的门票信息,无法驳回");
            }
            // 拒绝审批
            UserTicketApply userTicketApply = new UserTicketApply();
            userTicketApply.setId(request.getId());
            userTicketApply.setStage(UserTicketApplyStage.REJECT.getCode());
            userTicketApply.setComment(request.getComment());
            userTicketApply.setModifyTime(this.getServerTime());
            userTicketApplyService.modifyById(userTicketApply);
            // 发送消息给用户
            ShopConfigDTO shopConfigDTO = shopConfigService.findByShopId(userTicketApplyDTO.getShopId());
            String shopName = "";
            if (shopConfigDTO != null) {
                shopName = shopConfigDTO.getName();
            }
            UserMessagePayload userMessagePayload = new UserMessagePayload();
            userMessagePayload.setUserId(userTicketApplyDTO.getUserId());
            userMessagePayload.setShopId(userTicketApplyDTO.getShopId());
            userMessagePayload.setTitle("报名审核拒绝");
            userMessagePayload.setContent(shopName + ",您的门票报名未通过审核，拒绝原因：" + request.getComment());
            userMessagePayload.setBusinessCatalog(BusinessCatalog.USER_TICKET_APPLY.getCode());
            userMessagePayload.setBusinessId(userTicketApplyDTO.getId());
            userMessagePayload.setBusinessId(userTicketApplyDTO.getId());
            redisTemplate.opsForList().leftPush(CacheKey.USER_MESSAGE_QUEUE, this.getJSON(userMessagePayload));
            return new Response<>(OK, SUCCESS);
        }catch (Exception e){
            logger.error(e.getMessage(),e);
            return new Response<>(ERROR, FAILURE);
        }finally {
            lock.unlock();
        }
    }

    /**
     * 删除用户门票报名
     */
    @RequestMapping(value = "/v1/user/ticket/apply/remove", method = RequestMethod.POST)
    public Response<?> remove(@RequestBody UserTicketApplyQuery request) {
        Date datetime = this.getServerTime();
        UserTicketApply userTicketApply = new UserTicketApply();
        userTicketApply.setId(request.getId());
        userTicketApply.setStatus(DataStatus.N.getCode());
        userTicketApply.setModifyTime(datetime);
        userTicketApplyService.modifyById(userTicketApply);
        return new Response<>(OK, SUCCESS);
    }


}
