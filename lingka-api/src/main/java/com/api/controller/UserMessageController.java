package com.api.controller;

import java.util.Date;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.api.config.Token;
import com.api.validator.UserMessageValidator;
import com.common.bean.Response;
import com.common.constant.BusinessCatalog;
import com.common.constant.DataStatus;
import com.common.constant.TokenType;
import com.common.constant.UserMessageReadStage;
import com.domain.UserMessage;
import com.dto.UserMessageDTO;
import com.query.UserMessageQuery;
import com.github.pagehelper.PageInfo;
import com.service.UserMessageService;

/**
 * 用户消息控制器
 *
 * <AUTHOR>
 * @date 2025-09-21
 */
@RestController
public class UserMessageController extends BaseController {

    @Autowired
    private UserMessageService userMessageService;

    /**
     * 分页查询用户消息列表
     */
    @RequestMapping(value = "/v1/user/message/query", method = RequestMethod.POST)
    @Token(tokenType = TokenType.C)
    public Response<PageInfo<UserMessageDTO>> query(@RequestBody UserMessageQuery userMessageQuery) {
        userMessageQuery.setStatus(DataStatus.Y.getCode());
        PageInfo<UserMessageDTO> pageInfo = userMessageService.find(userMessageQuery);
        for (UserMessageDTO userMessageDTO : pageInfo.getList()) {
            userMessageDTO.setBusinessCatalogName(BusinessCatalog.getName(userMessageDTO.getBusinessCatalog()));
            userMessageDTO.setReadStageName(UserMessageReadStage.getName(userMessageDTO.getReadStage()));
        }
        return new Response<>(pageInfo);
    }

    /**
     * 分页查询我的消息列表
     */
    @RequestMapping(value = "/v1/user/message/my/query", method = RequestMethod.POST)
    @Token(tokenType = TokenType.C)
    public Response<PageInfo<UserMessageDTO>> queryMy(@RequestBody UserMessageQuery request) {
       request.setUserId(this.getUserToken().getId());
        return this.query(request);
    }


//    /**
//     * 批量消息标记为已/未读
//     */
//    @RequestMapping(value = "/v1/user/message/read/stage/modify", method = RequestMethod.POST)
//    public Response<PageInfo<UserMessageDTO>> modifyReadStage(@RequestBody UserMessageQuery request) {
//        UserMessageValidator userMessageValidator = new UserMessageValidator();
//        if (!userMessageValidator.onId(request.getId()).onReadStage(request.getReadStage()).result()) {
//            return new Response<>(ERROR, userMessageValidator.getErrorMessage());
//        }
//        UserMessage userMessage = new UserMessage();
//        userMessage.setId(request.getId());
//        userMessage.setReadStage(request.getReadStage());
//        userMessage.setModifyTime(this.getServerTime());
//        userMessageService.modifyById(userMessage);
//        return new Response<>(OK, SUCCESS);
//    }

    /**
     * 单个消息标记为已/未读
     */
    @RequestMapping(value = "/v1/user/message/read/stage/modify", method = RequestMethod.POST)
    @Token(tokenType = TokenType.C)
    public Response<PageInfo<UserMessageDTO>> modifyReadStage(@RequestBody UserMessageQuery request) {
        UserMessageValidator userMessageValidator = new UserMessageValidator();
        if (!userMessageValidator.onId(request.getId()).onReadStage(request.getReadStage()).result()) {
            return new Response<>(ERROR, userMessageValidator.getErrorMessage());
        }
        UserMessage userMessage = new UserMessage();
        userMessage.setId(request.getId());
        userMessage.setReadStage(request.getReadStage());
        userMessage.setModifyTime(this.getServerTime());
        userMessageService.modifyById(userMessage);
        return new Response<>(OK, SUCCESS);
    }


    /**
     * 批量删除用户消息
     */
    @RequestMapping(value = "/v1/user/message/batch/remove", method = RequestMethod.POST)
    @Token(tokenType = TokenType.C)
    public Response<?> removeByBatch(@RequestBody UserMessageQuery request) {
        UserMessageValidator userMessageValidator = new UserMessageValidator();
        if (!userMessageValidator.onIds(request.getIds()).result()) {
            return new Response<>(ERROR, userMessageValidator.getErrorMessage());
        }
        Date datetime = this.getServerTime();
        for (Long id : request.getIds()) {
            UserMessage userMessage = new UserMessage();
            userMessage.setId(id);
            userMessage.setStatus(DataStatus.N.getCode());
            userMessage.setModifyTime(datetime);
            userMessageService.modifyById(userMessage);
        }
        return new Response<>(OK, SUCCESS);
    }

    /**
     * 单个删除用户消息
     */
    @RequestMapping(value = "/v1/user/message/remove", method = RequestMethod.POST)
    @Token(tokenType = TokenType.C)
    public Response<?> remove(@RequestBody UserMessageQuery request) {
        UserMessageValidator userMessageValidator = new UserMessageValidator();
        if (!userMessageValidator.onId(request.getId()).result()) {
            return new Response<>(ERROR, userMessageValidator.getErrorMessage());
        }
        Date datetime = this.getServerTime();
        UserMessage userMessage = new UserMessage();
        userMessage.setId(request.getId());
        userMessage.setStatus(DataStatus.N.getCode());
        userMessage.setModifyTime(datetime);
        userMessageService.modifyById(userMessage);
        return new Response<>(OK, SUCCESS);
    }


}
