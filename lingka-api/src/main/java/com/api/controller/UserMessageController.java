package com.api.controller;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.common.bean.Response;
import com.common.constant.DataStatus;
import com.domain.UserMessage;
import com.dto.UserMessageDTO;
import com.query.UserMessageQuery;
import com.github.pagehelper.PageInfo;
import com.service.UserMessageService;

/**
 * 用户消息控制器
 * 
 * <AUTHOR>
 * @date 2025-09-21
 */
@RestController
public class UserMessageController extends BaseController {

    @Autowired
    private UserMessageService userMessageService;

	/**
	 * 增加用户消息
	 */
	@RequestMapping(value = "/v1/user/message/create", method = RequestMethod.POST)
	public Response<?> create(@RequestBody UserMessage userMessage) {
			Date datetime = this.getServerTime();
			userMessage.setStatus(DataStatus.Y.getCode());
			userMessage.setModifyTime(datetime);
			userMessage.setCreateTime(datetime);
			userMessageService.create(userMessage);
        	return new Response<>(OK,SUCCESS);
	}


	/**
	 * 分页查询用户消息列表
	 */
	@RequestMapping(value = "/v1/user/message/query", method = RequestMethod.POST)
	public Response<PageInfo<UserMessageDTO>> query(@RequestBody UserMessageQuery userMessageQuery){
			userMessageQuery.setStatus(DataStatus.Y.getCode());
		PageInfo<UserMessageDTO> pageInfo = userMessageService.find(userMessageQuery);
		return new Response<>(pageInfo);
	}

	/**
	 * 全部查询用户消息列表
	 */
	@RequestMapping(value = "/v1/user/message/all/query", method = RequestMethod.POST)
	public Response<List<UserMessageDTO>> queryAll(@RequestBody UserMessageQuery userMessageQuery){
			userMessageQuery.setStatus(DataStatus.Y.getCode());
		List<UserMessageDTO> userMessageDTOs = userMessageService.findAll(userMessageQuery);
		return new Response<>(userMessageDTOs);
	}


    /**
     * 修改用户消息
     */
    @RequestMapping(value = "/v1/user/message/modify", method = RequestMethod.POST)
    public Response<?> modify(@RequestBody UserMessage userMessage){
		Date datetime = this.getServerTime();
		userMessage.setModifyTime(datetime);
        userMessageService.modifyById(userMessage);
	    return new Response<>(OK,SUCCESS);
    }

	/**
	 * 删除用户消息
	 */
	@RequestMapping(value = "/v1/user/message/remove", method = RequestMethod.POST)
	public Response<?> remove(@RequestBody UserMessageQuery request){
		Date datetime = this.getServerTime();
		UserMessage userMessage = new UserMessage();
		userMessage.setId(request.getId());
		userMessage.setStatus(DataStatus.N.getCode());
		userMessage.setModifyTime(datetime);
			userMessageService.modifyById(userMessage);
		return new Response<>(OK,SUCCESS);
	}


}
