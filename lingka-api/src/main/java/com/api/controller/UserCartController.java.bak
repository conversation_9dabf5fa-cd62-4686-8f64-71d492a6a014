package com.api.controller;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.locks.Lock;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.api.config.Token;
import com.api.constant.App;
import com.api.validator.UserCartValidator;
import com.common.bean.Response;
import com.common.constant.CacheKey;
import com.common.constant.DataStatus;
import com.common.constant.OrderStage;
import com.common.constant.TokenType;
import com.common.constant.UserCartStatus;
import com.common.constant.UserTicketStage;
import com.common.util.DateUtil;
import com.common.util.StringUtil;
import com.domain.UserCart;
import com.dto.CartDTO;
import com.dto.OrderDTO;
import com.dto.OrderProductDTO;
import com.dto.ProductDTO;
import com.dto.ProductIngredientDTO;
import com.dto.ProductOptionDTO;
import com.dto.ProductSkuDTO;
import com.dto.TicketDTO;
import com.dto.TicketProductDTO;
import com.dto.UserCartDTO;
import com.dto.UserTicketDTO;
import com.query.OrderProductQuery;
import com.query.OrderQuery;
import com.query.TicketProductQuery;
import com.query.UserCartQuery;
import com.query.UserTicketQuery;
import com.service.OrderProductService;
import com.service.OrderService;
import com.service.ProductIngredientService;
import com.service.ProductOptionService;
import com.service.ProductService;
import com.service.ProductSkuService;
import com.service.TicketProductService;
import com.service.TicketService;
import com.service.UserCartService;
import com.service.UserTicketService;

/**
 * 用户购物车
 *
 * <AUTHOR>
 * @date 2025-08-30
 */
@RestController
public class UserCartController extends BaseController {

    @Autowired
    private UserCartService userCartService;
    @Autowired
    private ProductService productService;
    @Autowired
    private ProductSkuService productSkuService;
    @Autowired
    private ProductOptionService productOPTIONService;
    @Autowired
    private ProductIngredientService productIngredientService;
    @Autowired
    private UserTicketService userTicketService;
    @Autowired
    private OrderService orderService;
    @Autowired
    private OrderProductService orderProductService;
    @Autowired
    private TicketProductService ticketProductService;
    @Autowired
    private TicketService ticketService;
    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    /**
     * 加入我的购物车
     */
    @RequestMapping(value = "/v1/user/cart/my/create", method = RequestMethod.POST)
    @Token(tokenType = {TokenType.C})
    public Response<?> createMy(@RequestBody UserCartQuery request) {
        UserCartValidator validator = new UserCartValidator();
        if (!validator.onShopId(request.getShopId()).onProductId(request.getProductId())
                .onNumber(request.getNumber()).onProductSkuId(request.getProductSkuId()).result()) {
            return new Response<>(ERROR, validator.getErrorMessage());
        }
        Lock lock = this.getLock(redisTemplate, CacheKey.LOCK_USER_CART, this.getUserToken().getId().toString());
        if (!lock.tryLock()) {
            return new Response<>(ERROR, "请勿重复提交");
        }
        try {
            // 查询该用户购物车里面是否已经有这个规格的产品
            Date datetime = this.getServerTime();
            UserCartQuery userCartQuery = new UserCartQuery();
            userCartQuery.setUserId(this.getUserToken().getId());
            userCartQuery.setShopId(request.getShopId());
            userCartQuery.setProductId(request.getProductId());
            userCartQuery.setProductSkuId(request.getProductSkuId());
            if (request.getProductOptionIdList() != null && !request.getProductOptionIdList().isEmpty()) {
                userCartQuery.setProductOptionIds(StringUtil.joinWithLong(request.getProductOptionIdList(), App.COMMA));
            }
            if (request.getProductIngredientIdList() != null && !request.getProductIngredientIdList().isEmpty()) {
                userCartQuery.setProductIngredientIds(StringUtil.joinWithLong(request.getProductIngredientIdList(), App.COMMA));
            }
            userCartQuery.setStatus(DataStatus.Y.getCode());
            List<UserCartDTO> userCartDTOs = userCartService.findAll(userCartQuery);
            if (userCartDTOs != null && !userCartDTOs.isEmpty()) {
                // 将购物车中原有的数量+1
                UserCart userCartModify = new UserCart();
                userCartModify.setId(userCartDTOs.get(0).getId());
                userCartModify.setNumber(userCartDTOs.get(0).getNumber() + request.getNumber());
                userCartModify.setModifyTime(datetime);
                userCartService.modifyById(userCartModify);
                return new Response<>();
            } else {
                UserCart userCartCreate = new UserCart();
                BeanUtils.copyProperties(request, userCartCreate);
                userCartCreate.setUserId(this.getUserToken().getId());
                if (request.getProductOptionIdList() != null && !request.getProductOptionIdList().isEmpty()) {
                    userCartCreate.setProductOptionIds(StringUtil.joinWithLong(request.getProductOptionIdList(), App.COMMA));
                }
                if (request.getProductIngredientIdList() != null && !request.getProductIngredientIdList().isEmpty()) {
                    userCartCreate.setProductIngredientIds(StringUtil.joinWithLong(request.getProductIngredientIdList(), App.COMMA));
                }
                userCartCreate.setUserCartStatus(DataStatus.Y.getCode());
                userCartCreate.setStatus(DataStatus.Y.getCode());
                userCartCreate.setModifyTime(datetime);
                userCartCreate.setCreateTime(datetime);
                userCartService.create(userCartCreate);
                return new Response<>();
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        } finally {
            lock.unlock();
        }
    }


    /**
     * 查询我的购物车
     */
    @RequestMapping(value = "/v1/user/cart/all/my/query", method = RequestMethod.POST)
    @Token(tokenType = {TokenType.C})
    public Response<CartDTO> queryMyAll(@RequestBody UserCartQuery request) {
        UserCartValidator validator = new UserCartValidator();
        if (!validator.onShopId(request.getShopId()).result()) {
            return new Response<>(ERROR, validator.getErrorMessage());
        }
        CartDTO cartDTO = new CartDTO();
        cartDTO.setTotalFinalPriceStr("0.00");
        cartDTO.setTotalOriginalPriceStr("0.00");
        cartDTO.setTotalReducedPriceStr("0.00");
        cartDTO.setUserCarts(new ArrayList<>());
        request.setUserId(this.getUserToken().getId());
        request.setStatus(DataStatus.Y.getCode());
        List<UserCartDTO> userCartDTOs = userCartService.findAll(request);
        if (userCartDTOs == null || userCartDTOs.isEmpty()) {
            return new Response<>(cartDTO);
        }
        Long userId = this.getUserToken().getId();
        // 查询购物车中的商品信息
        List<Long> productIds = new ArrayList<>();
        List<Long> productSkuIds = new ArrayList<>();
        List<Long> productOptionIds = new ArrayList<>();
        List<Long> productIngredientIds = new ArrayList<>();
        for (UserCartDTO userCartDTO : userCartDTOs) {
            productIds.add(userCartDTO.getProductId());
            productSkuIds.add(userCartDTO.getProductSkuId());
            if (userCartDTO.getProductOptionIds() != null && !userCartDTO.getProductOptionIds().isEmpty()) {
                productOptionIds.addAll(StringUtil.splitWithLong(userCartDTO.getProductOptionIds(), App.COMMA));
            }
            if (userCartDTO.getProductIngredientIds() != null && !userCartDTO.getProductIngredientIds().isEmpty()) {
                productIngredientIds.addAll(StringUtil.splitWithLong(userCartDTO.getProductIngredientIds(), App.COMMA));
            }
        }
        Map<Long, ProductDTO> productDTOMap = productService.findMapByIds(productIds);
        Map<Long, ProductSkuDTO> productSkuDTOMap = productSkuService.findMapByIds(productSkuIds);
        Map<Long, ProductOptionDTO> productOptionDTOMap = productOPTIONService.findMapByIds(productOptionIds);
        Map<Long, ProductIngredientDTO> productIngredientDTOMap = productIngredientService.findMapByIds(productIngredientIds);
        // 首先获取当前的时间，来判断应该查询哪天的门票
        Date datetime = this.getServerTime();
        // 分隔时间节点
        Date splitTime = this.getDatetime(DateUtil.format(datetime, DATE_FORMAT) + " " + com.common.constant.App.USER_TICKET_SPLIT_TIME, DATETIME_FORMAT);
        // 以当前时间的中午十二点为界限，当日中午十二点前生成的是前一日，当日中午十二点后生成的是当日
        Date applyTime = DateUtil.getDayStartTime(datetime.before(splitTime) ? DateUtil.getFutureDay(datetime, -1) : datetime);
        // 查询用户是否购买了门票
        UserTicketQuery userTicketQuery = new UserTicketQuery();
        userTicketQuery.setUserId(userId);
        userTicketQuery.setApplyTime(applyTime);
        userTicketQuery.setStatus(DataStatus.Y.getCode());
        userTicketQuery.setStage(UserTicketStage.VERIFIED.getCode());
        List<UserTicketDTO> userTicketDTOs = userTicketService.findAll(userTicketQuery);
        // 如果用户购买了门票，则查询对应商品的折扣
        Map<Long, Integer> productIdFreeNumberMap = new HashMap<>();
        int totalFreeProductNumber = 0;
        if (userTicketDTOs != null && !userTicketDTOs.isEmpty()) {
            UserTicketDTO userTicketResult = userTicketDTOs.get(0);
            // 查询这个门票可以免几杯
            TicketDTO ticketDTO = ticketService.findById(userTicketResult.getTicketId());
            if (ticketDTO != null) {
                totalFreeProductNumber = ticketDTO.getProductNumber();
            }
            // 查询这个门票对应了哪些商品
            TicketProductQuery ticketProductQuery = new TicketProductQuery();
            ticketProductQuery.setTicketId(userTicketResult.getTicketId());
            ticketProductQuery.setStatus(DataStatus.Y.getCode());
            List<TicketProductDTO> ticketProductDTOS = ticketProductService.findAll(ticketProductQuery);
            for (TicketProductDTO ticketProductDTO : ticketProductDTOS) {
                productIdFreeNumberMap.put(ticketProductDTO.getProductId(), ticketProductDTO.getProductNumber());
            }
        }
        // 查询用户的订单，统计在本门票的可用时间范围内用户都买了哪些商品
        OrderQuery orderQuery = new OrderQuery();
        orderQuery.setUserId(userId);
        Date minOrderTime;
        Date maxOrderTime;
        // 判断时间节点，来确定最小下单时间和最大下单时间
        if (datetime.before(splitTime)) {
            minOrderTime = splitTime;
            maxOrderTime = DateUtil.getFutureDay(splitTime, -1);
        } else {
            minOrderTime = splitTime;
            maxOrderTime = DateUtil.getFutureDay(splitTime, 1);
        }
        orderQuery.setMinOrderTime(minOrderTime);
        orderQuery.setMaxOrderTime(maxOrderTime);
        orderQuery.setShopId(request.getShopId());
        List<String> orderStages = new ArrayList<>();
        orderStages.add(OrderStage.WAIT_PAY.getCode());
        orderStages.add(OrderStage.PRODUCTION.getCode());
        orderStages.add(OrderStage.FINISH.getCode());
        orderQuery.setOrderStages(orderStages);
        orderQuery.setStatus(DataStatus.Y.getCode());
        List<OrderDTO> orderDTOs = orderService.findAll(orderQuery);
        List<Long> orderIds = new ArrayList<>();
        for (OrderDTO orderDTO : orderDTOs) {
            orderIds.add(orderDTO.getId());
        }
        if (!orderIds.isEmpty()) {
            // 查询这个订单对应的产品
            OrderProductQuery orderProductQuery = new OrderProductQuery();
            orderProductQuery.setOrderIds(orderIds);
            orderProductQuery.setStatus(DataStatus.Y.getCode());
            List<OrderProductDTO> orderProductDTOS = orderProductService.findAll(orderProductQuery);
            for (OrderProductDTO orderProductDTO : orderProductDTOS) {
                // 统计用户在本门票的可用时间范围内都买了哪些商品,将他们可用的数量给减去
                if (productIdFreeNumberMap.containsKey(orderProductDTO.getProductId())) {
                    // 将剩余可免单数量减去
                    totalFreeProductNumber = totalFreeProductNumber - orderProductDTO.getNumber();
                    Integer remainNumber = productIdFreeNumberMap.get(orderProductDTO.getProductId());
                    remainNumber = remainNumber - orderProductDTO.getNumber();
                    productIdFreeNumberMap.put(orderProductDTO.getProductId(), remainNumber);
                }
            }
        }
        // 对这个购物车的商品都附上价格 - 创建深拷贝
        List<UserCartDTO> userCartDTOsCopy = new ArrayList<>();
        for (UserCartDTO original : userCartDTOs) {
            UserCartDTO copy = new UserCartDTO();
            // 复制所有必要的字段
            copy.setId(original.getId());
            copy.setUserId(original.getUserId());
            copy.setShopId(original.getShopId());
            copy.setProductId(original.getProductId());
            copy.setProductSkuId(original.getProductSkuId());
            copy.setProductOptionIds(original.getProductOptionIds());
            copy.setProductIngredientIds(original.getProductIngredientIds());
            copy.setNumber(original.getNumber());
            copy.setUserCartStatus(original.getUserCartStatus());
            copy.setStatus(original.getStatus());
            copy.setModifyTime(original.getModifyTime());
            copy.setCreateTime(original.getCreateTime());
            userCartDTOsCopy.add(copy);
        }
        for (UserCartDTO userCartDTO : userCartDTOsCopy) {
            // 定义产品总价
            BigDecimal productSkuIngredientPrice = new BigDecimal("0.00");
            if (productSkuDTOMap.containsKey(userCartDTO.getProductSkuId())) {
                productSkuIngredientPrice = productSkuIngredientPrice.add(productSkuDTOMap.get(userCartDTO.getProductSkuId()).getPrice());
            }
            // 需要把小料的价格也加上去
            if (userCartDTO.getProductIngredientIds() != null && !userCartDTO.getProductIngredientIds().isEmpty()) {
                List<Long> productIngredientIdList = StringUtil.splitWithLong(userCartDTO.getProductIngredientIds(), App.COMMA);
                for (Long productIngredientId : productIngredientIdList) {
                    if (productIngredientDTOMap.containsKey(productIngredientId)) {
                        productSkuIngredientPrice = productSkuIngredientPrice.add(productIngredientDTOMap.get(productIngredientId).getPrice());
                    }
                }
            }
            userCartDTO.setProductSkuIngredientPrice(productSkuIngredientPrice);
        }
        // 按照产品价格进行倒序排序，如果产品价格相同，则按照数量进行倒序排序
        userCartDTOsCopy.sort((c1, c2) -> {
            // 价格倒序比较：价格高的在前
            int priceCompare = c2.getProductSkuIngredientPrice().compareTo(c1.getProductSkuIngredientPrice());
            if (priceCompare != 0) {
                return priceCompare;
            }
            // 价格相同时，数量倒序比较：数量多的在前
            return Integer.compare(c2.getNumber(), c1.getNumber());
        });
        // 处理userCartDTOsCopy,来填充实际的价格
        for (UserCartDTO userCartDTO : userCartDTOsCopy) {
            // 原价
            BigDecimal originalPrice = new BigDecimal("0.00");
            // 最终价
            BigDecimal finalPrice = new BigDecimal("0.00");
            // 减免价(计算减免的金额)
            BigDecimal reducedPrice = new BigDecimal("0.00");
            // 如果可以被免单，则判断这个商品可以被免单的数量
            // 本次可免单的产品数量
            int freeProductNumber = 0;
            if (productIdFreeNumberMap.containsKey(userCartDTO.getProductId())) {
                freeProductNumber = productIdFreeNumberMap.get(userCartDTO.getProductId());
            }
            for (int i = 0; i < userCartDTO.getNumber(); i++) {
                // 判断实际的价格
                if (totalFreeProductNumber > 0 && freeProductNumber > 0) {
                    reducedPrice = reducedPrice.add(userCartDTO.getProductSkuIngredientPrice());
                    freeProductNumber--;
                    totalFreeProductNumber--;
                } else {
                    finalPrice = finalPrice.add(userCartDTO.getProductSkuIngredientPrice());
                }
                originalPrice = originalPrice.add(userCartDTO.getProductSkuIngredientPrice());
            }
            userCartDTO.setOriginalPrice(originalPrice);
            userCartDTO.setFinalPrice(finalPrice);
            userCartDTO.setReducedPrice(reducedPrice);
        }
        Map<Long, UserCartDTO> userCartDTOCopyMap = new HashMap<>();
        for (UserCartDTO userCartDTO : userCartDTOsCopy) {
            userCartDTOCopyMap.put(userCartDTO.getId(), userCartDTO);
        }
        // 原价
        BigDecimal totalOriginalPrice = new BigDecimal("0.00");
        // 最终价
        BigDecimal totalFinalPrice = new BigDecimal("0.00");
        // 减免价(计算减免的金额)
        BigDecimal totalReducedPrice = new BigDecimal("0.00");
        for (UserCartDTO userCartDTO : userCartDTOs) {
            if (productDTOMap.containsKey(userCartDTO.getProductId())) {
                userCartDTO.setProductName(productDTOMap.get(userCartDTO.getProductId()).getName());
                userCartDTO.setProductCover(productDTOMap.get(userCartDTO.getProductId()).getPhoto());
            }
            if (productSkuDTOMap.containsKey(userCartDTO.getProductSkuId())) {
                String productName = productSkuDTOMap.get(userCartDTO.getProductSkuId()).getSize() + productSkuDTOMap.get(userCartDTO.getProductSkuId()).getUnit();
                userCartDTO.setProductSkuName(productName);
            }
            if (userCartDTO.getProductOptionIds() != null && !userCartDTO.getProductOptionIds().isEmpty()) {
                List<Long> productOptionIdList = StringUtil.splitWithLong(userCartDTO.getProductOptionIds(), App.COMMA);
                userCartDTO.setProductOptionIdList(productOptionIdList);
                List<String> productOptionNameList = new ArrayList<>();
                for (Long productOptionId : productOptionIdList) {
                    if (productOptionDTOMap.containsKey(productOptionId)) {
                        productOptionNameList.add(productOptionDTOMap.get(productOptionId).getValue());
                    }
                }
                userCartDTO.setProductOptionNameList(productOptionNameList);
            }
            if (userCartDTO.getProductIngredientIds() != null && !userCartDTO.getProductIngredientIds().isEmpty()) {
                List<Long> productIngredientIdList = StringUtil.splitWithLong(userCartDTO.getProductIngredientIds(), App.COMMA);
                userCartDTO.setProductIngredientIdList(productIngredientIdList);
                List<String> productIngredientNameList = new ArrayList<>();
                for (Long productIngredientId : productIngredientIdList) {
                    if (productIngredientDTOMap.containsKey(productIngredientId)) {
                        productIngredientNameList.add(productIngredientDTOMap.get(productIngredientId).getName());
                    }
                }
                userCartDTO.setProductIngredientNameList(productIngredientNameList);
            }
            // 购物车中的产品是无效的
            if (!UserCartStatus.Y.getCode().equals(userCartDTO.getUserCartStatus())) {
                continue;
            }
            UserCartDTO userCartDTOCopy = userCartDTOCopyMap.get(userCartDTO.getId());
            BigDecimal originalPrice = userCartDTOCopy.getOriginalPrice();
            BigDecimal finalPrice = userCartDTOCopy.getFinalPrice();
            BigDecimal reducedPrice = userCartDTOCopy.getReducedPrice();
            totalOriginalPrice = totalOriginalPrice.add(originalPrice);
            totalFinalPrice = totalFinalPrice.add(finalPrice);
            totalReducedPrice = totalReducedPrice.add(reducedPrice);
            userCartDTO.setProductSkuIngredientPriceStr(userCartDTOCopy.getProductSkuIngredientPrice().toString());
            userCartDTO.setOriginalPriceStr(originalPrice.toString());
            userCartDTO.setFinalPriceStr(finalPrice.toString());
            userCartDTO.setReducedPriceStr(reducedPrice.toString());
        }
        cartDTO.setUserCarts(userCartDTOs);
        cartDTO.setTotalOriginalPriceStr(totalOriginalPrice.toString());
        cartDTO.setTotalFinalPriceStr(totalFinalPrice.toString());
        cartDTO.setTotalReducedPriceStr(totalReducedPrice.toString());
        return new Response<>(cartDTO);
    }


    /**
     * 查询我的购物车总价
     */
    @RequestMapping(value = "/v1/user/cart/total/price/my/query", method = RequestMethod.POST)
    @Token(tokenType = {TokenType.C})
    public Response<CartDTO> queryMyTotalPrice(@RequestBody UserCartQuery request) {
        UserCartValidator validator = new UserCartValidator();
        if (!validator.onIds(request.getIds()).onShopId(request.getShopId()).result()) {
            return new Response<>(ERROR, validator.getErrorMessage());
        }
        CartDTO cartDTO = new CartDTO();
        cartDTO.setTotalOriginalPriceStr("0.00");
        cartDTO.setTotalFinalPriceStr("0.00");
        cartDTO.setTotalReducedPriceStr("0.00");
        request.setUserId(this.getUserToken().getId());
        request.setStatus(DataStatus.Y.getCode());
        List<UserCartDTO> userCartDTOs = userCartService.findByIds(request.getIds());
        if (userCartDTOs == null || userCartDTOs.isEmpty()) {
            return new Response<>(cartDTO);
        }
        // 查询购物车中的商品信息
        Long userId = this.getUserToken().getId();
        List<Long> productIds = new ArrayList<>();
        List<Long> productSkuIds = new ArrayList<>();
        List<Long> productIngredientIds = new ArrayList<>();
        for (UserCartDTO userCartDTO : userCartDTOs) {
            productIds.add(userCartDTO.getProductId());
            productSkuIds.add(userCartDTO.getProductSkuId());
            if (userCartDTO.getProductIngredientIds() != null && !userCartDTO.getProductIngredientIds().isEmpty()) {
                productIngredientIds.addAll(StringUtil.splitWithLong(userCartDTO.getProductIngredientIds(), App.COMMA));
            }
        }
        Map<Long, ProductDTO> productDTOMap = productService.findMapByIds(productIds);
        Map<Long, ProductSkuDTO> productSkuDTOMap = productSkuService.findMapByIds(productSkuIds);
        Map<Long, ProductIngredientDTO> productIngredientDTOMap = productIngredientService.findMapByIds(productIngredientIds);
        // 首先获取当前的时间，来判断应该查询哪天的门票
        Date datetime = this.getServerTime();
        // 分隔时间节点
        Date splitTime = this.getDatetime(DateUtil.format(datetime, DATE_FORMAT) + " " + com.common.constant.App.USER_TICKET_SPLIT_TIME, DATETIME_FORMAT);
        // 以当前时间的中午十二点为界限，当日中午十二点前生成的是前一日，当日中午十二点后生成的是当日
        Date applyTime = DateUtil.getDayStartTime(datetime.before(splitTime) ? DateUtil.getFutureDay(datetime, -1) : datetime);
        // 查询用户是否购买了门票
        UserTicketQuery userTicketQuery = new UserTicketQuery();
        userTicketQuery.setUserId(userId);
        userTicketQuery.setApplyTime(applyTime);
        userTicketQuery.setStatus(DataStatus.Y.getCode());
        userTicketQuery.setStage(UserTicketStage.VERIFIED.getCode());
        List<UserTicketDTO> userTicketDTOs = userTicketService.findAll(userTicketQuery);
        // 如果用户购买了门票，则查询对应商品的折扣
        Map<Long, Integer> productIdFreeNumberMap = new HashMap<>();
        int totalFreeProductNumber = 0;
        if (userTicketDTOs != null && !userTicketDTOs.isEmpty()) {
            UserTicketDTO userTicketResult = userTicketDTOs.get(0);
            // 查询这个门票可以免几杯
            TicketDTO ticketDTO = ticketService.findById(userTicketResult.getTicketId());
            if (ticketDTO != null) {
                totalFreeProductNumber = ticketDTO.getProductNumber();
            }
            // 查询这个门票对应了哪些商品
            TicketProductQuery ticketProductQuery = new TicketProductQuery();
            ticketProductQuery.setTicketId(userTicketResult.getTicketId());
            ticketProductQuery.setStatus(DataStatus.Y.getCode());
            List<TicketProductDTO> ticketProductDTOS = ticketProductService.findAll(ticketProductQuery);
            for (TicketProductDTO ticketProductDTO : ticketProductDTOS) {
                productIdFreeNumberMap.put(ticketProductDTO.getProductId(), ticketProductDTO.getProductNumber());
            }
        }
        // 查询用户的订单，统计在本门票的可用时间范围内用户都买了哪些商品
        OrderQuery orderQuery = new OrderQuery();
        orderQuery.setUserId(userId);
        Date minOrderTime;
        Date maxOrderTime;
        // 判断时间节点，来确定最小下单时间和最大下单时间
        if (datetime.before(splitTime)) {
            minOrderTime = splitTime;
            maxOrderTime = DateUtil.getFutureDay(splitTime, -1);
        } else {
            minOrderTime = splitTime;
            maxOrderTime = DateUtil.getFutureDay(splitTime, 1);
        }
        orderQuery.setMinOrderTime(minOrderTime);
        orderQuery.setMaxOrderTime(maxOrderTime);
        orderQuery.setShopId(request.getShopId());
        List<String> orderStages = new ArrayList<>();
        orderStages.add(OrderStage.WAIT_PAY.getCode());
        orderStages.add(OrderStage.PRODUCTION.getCode());
        orderStages.add(OrderStage.FINISH.getCode());
        orderQuery.setOrderStages(orderStages);
        orderQuery.setStatus(DataStatus.Y.getCode());
        List<OrderDTO> orderDTOs = orderService.findAll(orderQuery);
        List<Long> orderIds = new ArrayList<>();
        for (OrderDTO orderDTO : orderDTOs) {
            orderIds.add(orderDTO.getId());
        }
        if (!orderIds.isEmpty()) {
            // 查询这个订单对应的产品
            OrderProductQuery orderProductQuery = new OrderProductQuery();
            orderProductQuery.setOrderIds(orderIds);
            orderProductQuery.setStatus(DataStatus.Y.getCode());
            List<OrderProductDTO> orderProductDTOS = orderProductService.findAll(orderProductQuery);
            for (OrderProductDTO orderProductDTO : orderProductDTOS) {
                // 统计用户在本门票的可用时间范围内都买了哪些商品,将他们可用的数量给减去
                if (productIdFreeNumberMap.containsKey(orderProductDTO.getProductId())) {
                    // 将剩余可免单数量减去
                    totalFreeProductNumber = totalFreeProductNumber - orderProductDTO.getNumber();
                    Integer remainNumber = productIdFreeNumberMap.get(orderProductDTO.getProductId());
                    remainNumber = remainNumber - orderProductDTO.getNumber();
                    productIdFreeNumberMap.put(orderProductDTO.getProductId(), remainNumber);
                }
            }
        }
        // 对这个购物车的商品都附上价格 - 创建深拷贝
        List<UserCartDTO> userCartDTOsCopy = new ArrayList<>();
        for (UserCartDTO original : userCartDTOs) {
            UserCartDTO copy = new UserCartDTO();
            // 复制所有必要的字段
            copy.setId(original.getId());
            copy.setUserId(original.getUserId());
            copy.setShopId(original.getShopId());
            copy.setProductId(original.getProductId());
            copy.setProductSkuId(original.getProductSkuId());
            copy.setProductOptionIds(original.getProductOptionIds());
            copy.setProductIngredientIds(original.getProductIngredientIds());
            copy.setNumber(original.getNumber());
            copy.setUserCartStatus(original.getUserCartStatus());
            copy.setStatus(original.getStatus());
            copy.setModifyTime(original.getModifyTime());
            copy.setCreateTime(original.getCreateTime());
            userCartDTOsCopy.add(copy);
        }
        for (UserCartDTO userCartDTO : userCartDTOsCopy) {
            // 定义产品SKu+小料总价
            BigDecimal productSkuIngredientPrice = new BigDecimal("0.00");
            if (productSkuDTOMap.containsKey(userCartDTO.getProductSkuId())) {
                productSkuIngredientPrice = productSkuIngredientPrice.add(productSkuDTOMap.get(userCartDTO.getProductSkuId()).getPrice());
            }
            // 需要把小料的价格也加上去
            if (userCartDTO.getProductIngredientIds() != null && !userCartDTO.getProductIngredientIds().isEmpty()) {
                List<Long> productIngredientIdList = StringUtil.splitWithLong(userCartDTO.getProductIngredientIds(), App.COMMA);
                for (Long productIngredientId : productIngredientIdList) {
                    if (productIngredientDTOMap.containsKey(productIngredientId)) {
                        productSkuIngredientPrice = productSkuIngredientPrice.add(productIngredientDTOMap.get(productIngredientId).getPrice());
                    }
                }
            }
            userCartDTO.setProductSkuIngredientPrice(productSkuIngredientPrice);
        }
        // 按照产品价格进行倒序排序，如果产品价格相同，则按照数量进行倒序排序
        userCartDTOsCopy.sort((c1, c2) -> {
            // 价格倒序比较：价格高的在前
            int priceCompare = c2.getProductSkuIngredientPrice().compareTo(c1.getProductSkuIngredientPrice());
            if (priceCompare != 0) {
                return priceCompare;
            }
            // 价格相同时，数量倒序比较：数量多的在前
            return Integer.compare(c2.getNumber(), c1.getNumber());
        });
        // 处理userCartDTOsCopy,来填充实际的价格
        for (UserCartDTO userCartDTO : userCartDTOsCopy) {
            // 原价
            BigDecimal originalPrice = new BigDecimal("0.00");
            // 最终价
            BigDecimal finalPrice = new BigDecimal("0.00");
            // 减免价(计算减免的金额)
            BigDecimal reducedPrice = new BigDecimal("0.00");
            // 如果可以被免单，则判断这个商品可以被免单的数量
            // 本次可免单的产品数量
            int freeProductNumber = 0;
            if (productIdFreeNumberMap.containsKey(userCartDTO.getProductId())) {
                freeProductNumber = productIdFreeNumberMap.get(userCartDTO.getProductId());
            }
            for (int i = 0; i < userCartDTO.getNumber(); i++) {
                // 判断实际的价格
                if (totalFreeProductNumber > 0 && freeProductNumber > 0) {
                    reducedPrice = reducedPrice.add(userCartDTO.getProductSkuIngredientPrice());
                    freeProductNumber--;
                    totalFreeProductNumber--;
                } else {
                    finalPrice = finalPrice.add(userCartDTO.getProductSkuIngredientPrice());
                }
                originalPrice = originalPrice.add(userCartDTO.getProductSkuIngredientPrice());
            }
            userCartDTO.setOriginalPrice(originalPrice);
            userCartDTO.setFinalPrice(finalPrice);
            userCartDTO.setReducedPrice(reducedPrice);
        }
        Map<Long, UserCartDTO> userCartDTOCopyMap = new HashMap<>();
        for (UserCartDTO userCartDTO : userCartDTOsCopy) {
            userCartDTOCopyMap.put(userCartDTO.getId(), userCartDTO);
        }
        // 原价
        BigDecimal totalPrice = new BigDecimal("0.00");
        // 最终价
        BigDecimal totalFinalPrice = new BigDecimal("0.00");
        // 减免价(计算减免的金额)
        BigDecimal totalReducedPrice = new BigDecimal("0.00");
        for (UserCartDTO userCartDTO : userCartDTOs) {
            if (productDTOMap.containsKey(userCartDTO.getProductId())) {
                userCartDTO.setProductName(productDTOMap.get(userCartDTO.getProductId()).getName());
                userCartDTO.setProductCover(productDTOMap.get(userCartDTO.getProductId()).getPhoto());
            }
            if (productSkuDTOMap.containsKey(userCartDTO.getProductSkuId())) {
                String productName = productSkuDTOMap.get(userCartDTO.getProductSkuId()).getSize() + productSkuDTOMap.get(userCartDTO.getProductSkuId()).getUnit();
                userCartDTO.setProductSkuName(productName);
            }
            if (userCartDTO.getProductOptionIds() != null && !userCartDTO.getProductOptionIds().isEmpty()) {
                List<Long> productOptionIdList = StringUtil.splitWithLong(userCartDTO.getProductOptionIds(), App.COMMA);
                userCartDTO.setProductOptionIdList(productOptionIdList);
                List<String> productOptionNameList = new ArrayList<>();
                userCartDTO.setProductOptionNameList(productOptionNameList);
            }
            if (userCartDTO.getProductIngredientIds() != null && !userCartDTO.getProductIngredientIds().isEmpty()) {
                List<Long> productIngredientIdList = StringUtil.splitWithLong(userCartDTO.getProductIngredientIds(), App.COMMA);
                userCartDTO.setProductIngredientIdList(productIngredientIdList);
                List<String> productIngredientNameList = new ArrayList<>();
                for (Long productIngredientId : productIngredientIdList) {
                    if (productIngredientDTOMap.containsKey(productIngredientId)) {
                        productIngredientNameList.add(productIngredientDTOMap.get(productIngredientId).getName());
                    }
                }
                userCartDTO.setProductIngredientNameList(productIngredientNameList);
            }
            // 购物车中的产品是无效的
            if (!UserCartStatus.Y.getCode().equals(userCartDTO.getUserCartStatus())) {
                continue;
            }
            UserCartDTO userCartDTOCopy = userCartDTOCopyMap.get(userCartDTO.getId());
            BigDecimal originalPrice = userCartDTOCopy.getOriginalPrice();
            BigDecimal finalPrice = userCartDTOCopy.getFinalPrice();
            BigDecimal reducedPrice = userCartDTOCopy.getReducedPrice();
            totalPrice = totalPrice.add(originalPrice);
            totalFinalPrice = totalFinalPrice.add(finalPrice);
            totalReducedPrice = totalReducedPrice.add(reducedPrice);
            userCartDTO.setProductSkuIngredientPriceStr(userCartDTOCopy.getProductSkuIngredientPrice().toString());
            userCartDTO.setOriginalPriceStr(originalPrice.toString());
            userCartDTO.setFinalPriceStr(finalPrice.toString());
            userCartDTO.setReducedPriceStr(reducedPrice.toString());
        }
        cartDTO.setUserCarts(userCartDTOs);
        cartDTO.setTotalOriginalPriceStr(totalPrice.toString());
        cartDTO.setTotalFinalPriceStr(totalFinalPrice.toString());
        cartDTO.setTotalReducedPriceStr(totalReducedPrice.toString());
        return new Response<>(cartDTO);
    }

    /**
     * 修改我的购物车
     */
    @RequestMapping(value = "/v1/user/cart/my/modify", method = RequestMethod.POST)
    @Token(tokenType = {TokenType.C})
    public Response<?> modifyMy(@RequestBody UserCartQuery request) {
        UserCartValidator validator = new UserCartValidator();
        if (!validator.onId(request.getId()).onNumber(request.getNumber()).result()) {
            return new Response<>(ERROR, validator.getErrorMessage());
        }
        Lock lock = this.getLock(redisTemplate, CacheKey.LOCK_USER_CART, this.getUserToken().getId().toString());
        if (!lock.tryLock()) {
            return new Response<>(ERROR, "请勿重复提交");
        }
        try {
            UserCartDTO userCartDTO = userCartService.findById(request.getId());
            if (userCartDTO == null || !DataStatus.Y.getCode().equals(userCartDTO.getStatus())) {
                return new Response<>(ERROR, "未查询到该购物车,无法修改");
            }
            if (!userCartDTO.getUserId().equals(this.getUserToken().getId())) {
                return new Response<>(ERROR, "该购物车不属于您,无法修改");
            }
            if (!UserCartStatus.Y.getCode().equals(userCartDTO.getUserCartStatus())) {
                return new Response<>(ERROR, "该购物车已失效,无法修改");
            }
            Date datetime = this.getServerTime();
            if (request.getNumber() == 0) {
                UserCart userCart = new UserCart();
                userCart.setId(request.getId());
                userCart.setStatus(DataStatus.N.getCode());
                userCart.setModifyTime(datetime);
                userCartService.modifyById(userCart);
                return new Response<>(OK, SUCCESS);
            }
            UserCart userCart = new UserCart();
            userCart.setId(request.getId());
            userCart.setNumber(request.getNumber());
            userCart.setModifyTime(datetime);
            userCartService.modifyById(userCart);
            return new Response<>(OK, SUCCESS);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        } finally {
            lock.unlock();
        }
    }

    /**
     * 删除购物车
     */
    @RequestMapping(value = "/v1/user/cart/remove", method = RequestMethod.POST)
    @Token(tokenType = {TokenType.C})
    public Response<?> remove(@RequestBody UserCartQuery request) {
        Lock lock = this.getLock(redisTemplate, CacheKey.LOCK_USER_CART, this.getUserToken().getId().toString());
        if (!lock.tryLock()) {
            return new Response<>(ERROR, "请勿重复提交");
        }
        try {
            Date datetime = this.getServerTime();
            UserCart userCart = new UserCart();
            userCart.setId(request.getId());
            userCart.setStatus(DataStatus.N.getCode());
            userCart.setModifyTime(datetime);
            userCartService.modifyById(userCart);
            return new Response<>(OK, SUCCESS);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        } finally {
            lock.unlock();
        }
    }


    /**
     * 清空我的购物车
     */
    @RequestMapping(value = "/v1/user/cart/reset", method = RequestMethod.POST)
    @Token(tokenType = {TokenType.C})
    public Response<?> reset(@RequestBody UserCartQuery request) {
        Lock lock = this.getLock(redisTemplate, CacheKey.LOCK_USER_CART, this.getUserToken().getId().toString());
        if (!lock.tryLock()) {
            return new Response<>(ERROR, "请勿重复提交");
        }
        try {
            // 查询我的购物车
            UserCartQuery userCartQuery = new UserCartQuery();
            userCartQuery.setUserId(this.getUserToken().getId());
            userCartQuery.setStatus(DataStatus.Y.getCode());
            List<UserCartDTO> userCartDTOs = userCartService.findAll(userCartQuery);
            if (userCartDTOs == null || userCartDTOs.isEmpty()) {
                return new Response<>(OK, SUCCESS);
            }
            Date datetime = this.getServerTime();
            List<UserCart> userCarts = new ArrayList<>();
            for (UserCartDTO userCartDTO : userCartDTOs) {
                UserCart userCart = new UserCart();
                userCart.setId(userCartDTO.getId());
                userCart.setStatus(DataStatus.N.getCode());
                userCart.setModifyTime(datetime);
                userCarts.add(userCart);
            }
            userCartService.batchModifyById(userCarts);
            return new Response<>(OK, SUCCESS);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        } finally {
            lock.unlock();
        }
    }

}
