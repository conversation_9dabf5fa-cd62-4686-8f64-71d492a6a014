package com.api.controller;

import java.math.BigDecimal;
import java.util.concurrent.locks.Lock;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.common.bean.Response;
import com.common.client.WechatPayClient;
import com.common.constant.CacheKey;
import com.common.constant.DataStatus;
import com.common.constant.OrderPayStage;
import com.dto.OrderDTO;
import com.dto.OrderPaymentDTO;
import com.query.OrderPaymentQuery;
import com.service.OrderPaymentService;
import com.service.OrderService;
import com.wechat.pay.java.service.partnerpayments.jsapi.JsapiService;
import com.wechat.pay.java.service.partnerpayments.jsapi.model.Amount;
import com.wechat.pay.java.service.partnerpayments.jsapi.model.PrepayRequest;
import com.wechat.pay.java.service.partnerpayments.jsapi.model.PrepayResponse;

@RestController
public class WechatPayController extends BaseController{

    @Autowired
    private OrderPaymentService orderPaymentService;

    @Autowired
    private OrderService orderService;
    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    /**
     * 查询支付前prepay_id
     * @return
     */
    @RequestMapping("/v1/wechat/pay/prepay/id/query")
    public Response<String> queryPrepayId(@RequestBody OrderPaymentQuery request) {
        if (request.getOrderId() == null){
            return new Response<>(ERROR,"订单ID不能为空");
        }
        // 加入分布式锁
        Lock lock = this.getLock(redisTemplate, CacheKey.LOCK_ORDER_PAYMENT, request.getOrderId().toString());
        if (!lock.tryLock()) {
            return new Response<>(ERROR, "请勿重复提交");
        }
        try {
            // 查询这个订单是否已经生成了支付单，并且prepay_id 没有过期
            OrderPaymentQuery orderPaymentQuery = new OrderPaymentQuery();
            orderPaymentQuery.setOrderId(request.getOrderId());
            orderPaymentQuery.setStatus(DataStatus.Y.getCode());
            orderPaymentQuery.setPayStage(OrderPayStage.N.getCode());
            OrderPaymentDTO orderPaymentDTO = orderPaymentService.findByMax(orderPaymentQuery);
            if (orderPaymentDTO != null && orderPaymentDTO.getPrepayId() != null && orderPaymentDTO.getExpireTime().after(this.getServerTime())){
                return new Response<>(orderPaymentDTO.getPrepayId());
            }
            // 查询订单支付信息，对接微信支付
            OrderDTO orderDTO = orderService.findById(request.getOrderId());
            if (orderDTO == null){
                return new Response<>(ERROR, "订单不存在");
            }
            // 判断该订单是否已经支付
            if (OrderPayStage.Y.getCode().equals(orderDTO.getPayStage())) {
                return new Response<>(ERROR, "订单已支付");
            }
            // 判断该订单是否金额大于0
            if (orderDTO.getFinalAmount().compareTo(new BigDecimal("0.00")) <= 0) {
                return new Response<>(ERROR, "订单金额为0，无需支付");
            }
            // 调用微信支付，发起JSAPI/小程序下单
            WechatPayClient wechatPayClient = new WechatPayClient();
            JsapiService jsapiService = wechatPayClient.getJsapiService();
            PrepayRequest prepayRequest = new PrepayRequest();
            Amount amount = new Amount();
            amount.setTotal(100);
            prepayRequest.setAmount(amount);
            // 服务商APPID
            prepayRequest.setSpAppid("wxa9d9651ae******");
//        request.setMchid("190000****");
            prepayRequest.setDescription("测试商品标题");
            prepayRequest.setNotifyUrl("https://notify_url");
            prepayRequest.setOutTradeNo("out_trade_no_001");
            // 调用下单方法，得到应答
            PrepayResponse response = jsapiService.prepay(prepayRequest);
            return new Response<>(OK, SUCCESS);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        } finally {
            lock.unlock();
        }
    }
}
