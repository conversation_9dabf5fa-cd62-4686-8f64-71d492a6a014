package com.api.controller;

import java.util.ArrayList;
import java.util.List;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.common.bean.CodeNameDescriptionResponse;
import com.common.bean.NameValuePairResponse;
import com.common.bean.Response;
import com.common.constant.Gender;
import com.common.constant.OrderMode;
import com.common.constant.OrderPayStage;
import com.common.constant.OrderStage;
import com.common.constant.OrderType;
import com.common.constant.PublicStage;
import com.common.constant.ShopState;
import com.common.constant.ShopStatus;
import com.common.constant.TableFlag;
import com.common.constant.TagCatalog;
import com.common.constant.TicketPriceCatalog;
import com.common.constant.TicketStage;
import com.common.constant.UserTicketApplyStage;
import com.common.constant.UserTicketCatalog;
import com.common.constant.UserTicketStage;

/**
 * 字典模块接口
 *
 * <AUTHOR>
 * @date 2025-08-01
 */
@RestController
public class DictionaryController extends BaseController {

    /**
     * 查询性别
     */
    @RequestMapping(value = "/v1/dictionary/gender/query", method = {RequestMethod.POST})
    public Response<List<NameValuePairResponse>> queryGender() {
        List<NameValuePairResponse> response = new ArrayList<>();
        for (Gender value : Gender.values()) {
            NameValuePairResponse nameValuePairResponse = new NameValuePairResponse();
            nameValuePairResponse.setCode(value.getCode());
            nameValuePairResponse.setName(value.getName());
            response.add(nameValuePairResponse);
        }
        return new Response<>(OK, SUCCESS, response);
    }


    /**
     * 查询标签分类枚举
     *
     */
    @RequestMapping(value = "/v1/dictionary/tag/catalog/query", method = {RequestMethod.POST})
    public Response<List<NameValuePairResponse>> queryTagCatalog() {
        List<NameValuePairResponse> response = new ArrayList<>();
        for (TagCatalog value : TagCatalog.values()) {
            NameValuePairResponse nameValuePairResponse = new NameValuePairResponse();
            nameValuePairResponse.setCode(value.getCode());
            nameValuePairResponse.setName(value.getName());
            response.add(nameValuePairResponse);
        }
        return new Response<>(OK, SUCCESS, response);
    }

    /**
     * 查询点单制
     *
     */
    @RequestMapping(value = "/v1/dictionary/order/mode/query", method = {RequestMethod.POST})
    public Response<List<CodeNameDescriptionResponse>> queryOrderMode() {
        List<CodeNameDescriptionResponse> response = new ArrayList<>();
        for (OrderMode value : OrderMode.values()) {
            CodeNameDescriptionResponse codeNameDescriptionResponse = new CodeNameDescriptionResponse();
            codeNameDescriptionResponse.setCode(value.getCode());
            codeNameDescriptionResponse.setName(value.getName());
            codeNameDescriptionResponse.setDescription(value.getDescription());
            response.add(codeNameDescriptionResponse);
        }
        return new Response<>(OK, SUCCESS, response);
    }

    /**
     * 查询营业状态
     *
     */
    @RequestMapping(value = "/v1/dictionary/shop/state/query", method = {RequestMethod.POST})
    public Response<List<NameValuePairResponse>> queryShopState() {
        List<NameValuePairResponse> response = new ArrayList<>();
        for (ShopState value : ShopState.values()) {
            NameValuePairResponse nameValuePairResponse = new NameValuePairResponse();
            nameValuePairResponse.setCode(value.getCode());
            nameValuePairResponse.setName(value.getDescription());
            response.add(nameValuePairResponse);
        }
        return new Response<>(OK, SUCCESS, response);
    }

    /**
     * 查询店铺状态枚举
     *
     */
    @RequestMapping(value = "/v1/dictionary/shop/status/query", method = {RequestMethod.POST})
    public Response<List<NameValuePairResponse>> queryShopStatus() {
        List<NameValuePairResponse> response = new ArrayList<>();
        for (ShopStatus value : ShopStatus.values()) {
            NameValuePairResponse nameValuePairResponse = new NameValuePairResponse();
            nameValuePairResponse.setCode(value.getCode());
            nameValuePairResponse.setName(value.getDescription());
            response.add(nameValuePairResponse);
        }
        return new Response<>(OK, SUCCESS, response);
    }

    /**
     * 查询公共上下架
     *
     */
    @RequestMapping(value = "/v1/dictionary/public/stage/query", method = {RequestMethod.POST})
    public Response<List<NameValuePairResponse>> queryPublicStage() {
        List<NameValuePairResponse> response = new ArrayList<>();
        for (PublicStage value : PublicStage.values()) {
            NameValuePairResponse nameValuePairResponse = new NameValuePairResponse();
            nameValuePairResponse.setCode(value.getCode());
            nameValuePairResponse.setName(value.getName());
            response.add(nameValuePairResponse);
        }
        return new Response<>(OK, SUCCESS, response);
    }


    /**
     * 查询是否群酒
     *
     */
    @RequestMapping(value = "/v1/dictionary/table/flag/query", method = {RequestMethod.POST})
    public Response<List<NameValuePairResponse>> queryTableFlag() {
        List<NameValuePairResponse> response = new ArrayList<>();
        for (TableFlag value : TableFlag.values()) {
            NameValuePairResponse nameValuePairResponse = new NameValuePairResponse();
            nameValuePairResponse.setCode(value.getCode());
            nameValuePairResponse.setName(value.getName());
            response.add(nameValuePairResponse);
        }
        return new Response<>(OK, SUCCESS, response);
    }

    /**
     * 查询门票上下架
     */
    @RequestMapping(value = "/v1/dictionary/ticket/stage/query", method = {RequestMethod.POST})
    public Response<List<NameValuePairResponse>> queryTicketStage() {
        List<NameValuePairResponse> response = new ArrayList<>();
        for (TicketStage value : TicketStage.values()) {
            NameValuePairResponse nameValuePairResponse = new NameValuePairResponse();
            nameValuePairResponse.setCode(value.getCode());
            nameValuePairResponse.setName(value.getName());
            response.add(nameValuePairResponse);
        }
        return new Response<>(OK, SUCCESS, response);
    }


    /**
     * 查询门票价格分类
     */
    @RequestMapping(value = "/v1/dictionary/ticket/price/catalog/query", method = {RequestMethod.POST})
    public Response<List<NameValuePairResponse>> queryTicketPriceCatalog() {
        List<NameValuePairResponse> response = new ArrayList<>();
        for (TicketPriceCatalog value : TicketPriceCatalog.values()) {
            NameValuePairResponse nameValuePairResponse = new NameValuePairResponse();
            nameValuePairResponse.setCode(value.getCode());
            nameValuePairResponse.setName(value.getName());
            response.add(nameValuePairResponse);
        }
        return new Response<>(OK, SUCCESS, response);
    }

    /**
     * 查询门票分类
     */
    @RequestMapping(value = "/v1/dictionary/user/ticket/catalog/query", method = {RequestMethod.POST})
    public Response<List<NameValuePairResponse>> queryUserTicketCatalog() {
        List<NameValuePairResponse> response = new ArrayList<>();
        for (UserTicketCatalog value : UserTicketCatalog.values()) {
            NameValuePairResponse nameValuePairResponse = new NameValuePairResponse();
            nameValuePairResponse.setCode(value.getCode());
            nameValuePairResponse.setName(value.getName());
            response.add(nameValuePairResponse);
        }
        return new Response<>(OK, SUCCESS, response);
    }

    /**
     * 查询门票是否核销
     */
    @RequestMapping(value = "/v1/dictionary/user/ticket/stage/query", method = {RequestMethod.POST})
    public Response<List<NameValuePairResponse>> queryUserTicketStage() {
        List<NameValuePairResponse> response = new ArrayList<>();
        for (UserTicketStage value : UserTicketStage.values()) {
            NameValuePairResponse nameValuePairResponse = new NameValuePairResponse();
            nameValuePairResponse.setCode(value.getCode());
            nameValuePairResponse.setName(value.getName());
            response.add(nameValuePairResponse);
        }
        return new Response<>(OK, SUCCESS, response);
    }

    /**
     * 查询订单一级状态(待付款、制作中、已完成、已退款、已取消···)
     */
    @RequestMapping(value = "/v1/dictionary/order/stage/query", method = {RequestMethod.POST})
    public Response<List<NameValuePairResponse>> queryOrderStage() {
        List<NameValuePairResponse> response = new ArrayList<>();
        for (OrderStage value : OrderStage.values()) {
            NameValuePairResponse nameValuePairResponse = new NameValuePairResponse();
            nameValuePairResponse.setCode(value.getCode());
            nameValuePairResponse.setName(value.getName());
            response.add(nameValuePairResponse);
        }
        return new Response<>(OK, SUCCESS, response);
    }

    /**
     * 查询订单支付状态
     */
    @RequestMapping(value = "/v1/dictionary/order/pay/stage/query", method = {RequestMethod.POST})
    public Response<List<NameValuePairResponse>> queryOrderPayStage() {
        List<NameValuePairResponse> response = new ArrayList<>();
        for (OrderPayStage value : OrderPayStage.values()) {
            NameValuePairResponse nameValuePairResponse = new NameValuePairResponse();
            nameValuePairResponse.setCode(value.getCode());
            nameValuePairResponse.setName(value.getName());
            response.add(nameValuePairResponse);
        }
        return new Response<>(OK, SUCCESS, response);
    }

    /**
     * 查询订单类型
     */
    @RequestMapping(value = "/v1/dictionary/order/type/query", method = {RequestMethod.POST})
    public Response<List<NameValuePairResponse>> queryOrderType() {
        List<NameValuePairResponse> response = new ArrayList<>();
        for (OrderType value : OrderType.values()) {
            NameValuePairResponse nameValuePairResponse = new NameValuePairResponse();
            nameValuePairResponse.setCode(value.getCode());
            nameValuePairResponse.setName(value.getName());
            response.add(nameValuePairResponse);
        }
        return new Response<>(OK, SUCCESS, response);
    }

    /**
     * 查询报名审核状态
     */
    @RequestMapping(value = "/v1/dictionary/user/ticket/apply/stage/query", method = {RequestMethod.POST})
    public Response<List<NameValuePairResponse>> queryUserTicketApplyStage() {
        List<NameValuePairResponse> response = new ArrayList<>();
        for (UserTicketApplyStage value : UserTicketApplyStage.values()) {
            NameValuePairResponse nameValuePairResponse = new NameValuePairResponse();
            nameValuePairResponse.setCode(value.getCode());
            nameValuePairResponse.setName(value.getName());
            response.add(nameValuePairResponse);
        }
        return new Response<>(OK, SUCCESS, response);
    }
}
