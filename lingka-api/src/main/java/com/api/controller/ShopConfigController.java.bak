package com.api.controller;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.api.config.Token;
import com.api.validator.ShopConfigValidator;
import com.api.validator.ShopValidator;
import com.common.bean.JsonWebToken;
import com.common.bean.Response;
import com.common.bean.WeChatMsgSecCheckAsyncRequest;
import com.common.bean.WeChatMsgSecCheckAsyncResponse;
import com.common.bean.WeChatMsgSecCheckRequest;
import com.common.bean.WeChatMsgSecCheckResponse;
import com.common.client.ALiOSSClient;
import com.common.client.WechatClient;
import com.common.constant.App;
import com.common.constant.AuditType;
import com.common.constant.DataStatus;
import com.common.constant.MsgCheckMediaType;
import com.common.constant.MsgCheckScene;
import com.common.constant.MsgCheckSuggest;
import com.common.constant.ShopConfigModifyAudit;
import com.common.constant.ShopState;
import com.common.constant.TokenType;
import com.common.util.LbsUtil;
import com.common.util.LbsUtil.Point;
import com.common.util.ReflectDiffUtils;
import com.domain.Audit;
import com.domain.ShopConfig;
import com.domain.ShopConfigModifyApply;
import com.domain.ShopOpenCloseTime;
import com.dto.CityDTO;
import com.dto.DistrictDTO;
import com.dto.ProvinceDTO;
import com.dto.ShopConfigDTO;
import com.dto.ShopConfigModifyApplyDTO;
import com.dto.ShopConfigWithUserRoleDTO;
import com.dto.ShopDTO;
import com.dto.ShopOpenCloseTimeDTO;
import com.dto.UserDTO;
import com.dto.UserRoleDTO;
import com.dto.WechatAppDTO;
import com.query.ShopConfigQuery;
import com.github.pagehelper.PageInfo;
import com.query.ShopOpenCloseTimeQuery;
import com.query.UserRoleQuery;
import com.service.AuditService;
import com.service.CityService;
import com.service.DistrictService;
import com.service.ProvinceService;
import com.service.ShopConfigModifyApplyService;
import com.service.ShopConfigService;
import com.service.ShopOpenCloseTimeService;
import com.service.ShopService;
import com.service.UserRoleService;
import com.service.UserService;
import com.service.WechatAppService;

/**
 * 店铺配置控制器
 *
 * <AUTHOR>
 */
@RestController
public class ShopConfigController extends BaseController {

    @Autowired
    private ShopConfigService shopConfigService;

    @Autowired
    private ShopService shopService;

    @Autowired
    private ShopConfigModifyApplyService shopConfigModifyApplyService;

    @Autowired
    private UserService userService;
    @Autowired
    private WechatAppService wechatAppService;
    @Autowired
    private AuditService auditService;
    @Autowired
    private ProvinceService provinceService;
    @Autowired
    private CityService cityService;
    @Autowired
    private DistrictService districtService;
    @Autowired
    private UserRoleService userRoleService;
    @Autowired
    private ShopOpenCloseTimeService shopOpenCloseTimeService;

    private Point shopConfigAddressToPoint(ShopConfigQuery shopConfig) {
        StringBuilder fullAddress = new StringBuilder();
        Long provinceId = shopConfig.getProvinceId();
        if (provinceId != null) {
            ProvinceDTO provinceDTO = provinceService.findById(provinceId);
            if (provinceDTO != null) {
                fullAddress.append(provinceDTO.getName());
            }
        }
        Long cityId = shopConfig.getCityId();
        if (cityId != null) {
            CityDTO cityDTO = cityService.findById(cityId);
            if (cityDTO != null && !fullAddress.toString().equals(cityDTO.getName())) {
                fullAddress.append(cityDTO.getName());
            }
        }
        Long distinctId = shopConfig.getDistinctId();
        if (distinctId != null) {
            DistrictDTO districtDTO = districtService.findById(distinctId);
            if (districtDTO != null) {
                fullAddress.append(districtDTO.getName());
            }
        }
        String address = shopConfig.getAddress();
        if (address != null && address.isEmpty()) {
            fullAddress.append(address);
        }
        if (fullAddress.length() > 0) {
            return LbsUtil.addressToPoint(fullAddress.toString());
        }
        return null;
    }

    private final List<Long> allowedEditRoleIds = Arrays.asList(10000000L, 10000001L);
    private final List<Long> allowedReadRoleIds = Arrays.asList(10000000L, 10000001L, 10000002L, 10000003L);

    private boolean hasPermission(Long userId, Long shopId, boolean forEdit) {
        UserRoleQuery userRoleQuery = new UserRoleQuery();
        userRoleQuery.setUserId(userId);
        userRoleQuery.setShopId(shopId);
        userRoleQuery.setStatus(DataStatus.Y.getCode());
        List<UserRoleDTO> userRoleDTOList = userRoleService.findAll(userRoleQuery);
        boolean hasPermission = false;
        if (forEdit) {
            for (UserRoleDTO userRoleDTO : userRoleDTOList) {
                if (allowedEditRoleIds.contains(userRoleDTO.getRoleId())) {
                    hasPermission = true;
                    break;
                }
            }
        } else {
            for (UserRoleDTO userRoleDTO : userRoleDTOList) {
                if (allowedReadRoleIds.contains(userRoleDTO.getRoleId())) {
                    hasPermission = true;
                    break;
                }
            }
        }

        return hasPermission;
    }

    /**
     * 增加店铺配置
     */
    @RequestMapping(value = "/v1/shop/config/create", method = RequestMethod.POST)
    @Token(tokenType = {TokenType.S, TokenType.C})
    public Response<?> create(@RequestBody ShopConfig shopConfig) {
        Long shopId = shopConfig.getShopId();
        if (shopId == null || shopId <= 0) {
            return new Response<>(ERROR, FAILURE + "：店铺ID不能为空");
        }
        ShopDTO shop = shopService.selectShopById(shopId);
        if (shop == null) {
            return new Response<>(ERROR, FAILURE + "：店铺不存在");
        }
        JsonWebToken token = this.getRawJsonWebToken();
        assert token != null;
        Long userId = token.getUserId();
        if (!TokenType.S.name().equals(token.getType())) {
            boolean hasPermission = hasPermission(userId, shopId, true);
            if (!hasPermission && !Objects.equals(shop.getOwnerUserId(), userId)) {
                return new Response<>(ERROR, FAILURE + "：您没有权限操作该店铺");
            }
        }
        ShopConfigQuery shopConfigQuery = new ShopConfigQuery();
        shopConfigQuery.setShopId(shopId);
        List<ShopConfigDTO> shopConfigDTOs = shopConfigService.findAll(shopConfigQuery);
        // 如果店铺配置已存在，则不允许重复创建
        if (shopConfigDTOs != null && !shopConfigDTOs.isEmpty()) {
            return new Response<>(ERROR, FAILURE + "：店铺配置已存在，请勿重复创建");
        }
        ShopValidator validator = new ShopValidator();
        if (shopConfigQuery.getAddress() != null) {
            if (!validator.onAddress(shopConfigQuery.getAddress()).result()) {
                return new Response<>(ERROR, validator.getErrorMessage());
            }
        }
        if (shopConfigQuery.getShippingTimeList() != null && !shopConfigQuery.getShippingTimeList().isEmpty()) {
            if (!validator.onShippingTime(shopConfigQuery.getShippingTimeList()).result()) {
                return new Response<>(ERROR, validator.getErrorMessage());
            }
        }
        // 设置初始营业状态为 close_manual
        shopConfig.setState(ShopState.CLOSE_MANUAL.getCode());
        if (shopConfig.getLatitude() == null && shopConfig.getLongitude() == null) {
            ShopConfigQuery query = new ShopConfigQuery();
            query.convertFromShopConfig(shopConfig);
            Point point = shopConfigAddressToPoint(query);
            if (point != null) {
                shopConfig.setLatitude(point.lat);
                shopConfig.setLongitude(point.lon);
            }
        }
        int count = 0;
        if (shopConfig.getShippingTimeList() != null) {
            List<ShopOpenCloseTime> shippingTimeList = shopConfig.getShippingTimeList();
            count += shopOpenCloseTimeService.removeByShopId(shopConfig.getShopId());
            for (ShopOpenCloseTime openCloseTime : shippingTimeList) {
                openCloseTime.setShopId(shopConfig.getShopId());
                ShopOpenCloseTime shopOpenCloseTime = new ShopOpenCloseTime();
                BeanUtils.copyProperties(openCloseTime, shopOpenCloseTime);
                count += shopOpenCloseTimeService.create(shopOpenCloseTime);
            }
        }
        count += shopConfigService.create(shopConfig);
        if (count <= 0) {
            return new Response<>(ERROR, FAILURE + "：店铺配置创建失败");
        }
        return new Response<>(OK, SUCCESS);
    }


    /**
     * 分页查询店铺配置列表
     */
    @RequestMapping(value = "/v1/shop/config/query", method = RequestMethod.POST)
    @Token(tokenType = {TokenType.S, TokenType.C})
    public Response<PageInfo<ShopConfigDTO>> query(@RequestBody ShopConfigQuery shopConfigQuery) {
        shopConfigQuery.setStatus(DataStatus.Y.getCode());
        PageInfo<ShopConfigDTO> pageInfo = shopConfigService.find(shopConfigQuery);
        return new Response<>(pageInfo);
    }

    /**
     * 全部查询店铺配置列表
     */
    @RequestMapping(value = "/v1/shop/config/all/query", method = RequestMethod.POST)
    @Token(tokenType = {TokenType.S, TokenType.C})
    public Response<List<ShopConfigDTO>> queryAll(@RequestBody ShopConfigQuery shopConfigQuery) {
        shopConfigQuery.setStatus(DataStatus.Y.getCode());
        List<ShopConfigDTO> shopConfigDTOs = shopConfigService.findAll(shopConfigQuery);
        return new Response<>(shopConfigDTOs);
    }


    /**
     * 修改店铺配置
     * <p>
     * Spring MVC call setter to bind request parameters to the query object.
     * {@link ShopConfigQuery#setPhoto(String[])} and {@link ShopConfigQuery#setTags(String[])}
     * are changed to convert string arrays into comma-separated string.
     */
    @RequestMapping(value = "/v1/shop/config/modify", method = RequestMethod.POST)
    @Token(tokenType = {TokenType.S, TokenType.C})
    public Response<?> modify(@RequestBody ShopConfigQuery shopConfigQuery) {
        shopConfigQuery.setStatus(null);
        ShopConfigDTO shopConfigDTOOld;
        if (shopConfigQuery.getId() != null) {
            shopConfigDTOOld = shopConfigService.findById(shopConfigQuery.getId());
        } else if (shopConfigQuery.getShopId() != null) {
            shopConfigDTOOld = shopConfigService.findByShopId(shopConfigQuery.getShopId());
            shopConfigQuery.setId(shopConfigDTOOld.getId());
        } else {
            return new Response<>(ERROR, FAILURE + "：店铺配置ID或店铺ID不能为空");
        }
        if (shopConfigDTOOld == null || Objects.equals(shopConfigDTOOld.getStatus(), DataStatus.N.getCode())) {
            return new Response<>(ERROR, FAILURE + "：店铺配置不存在，无法修改");
        }
        JsonWebToken token = this.getRawJsonWebToken();
        if (token == null) {
            return new Response<>(ERROR, FAILURE + "：您没有权限操作该店铺");
        }
        Long userId = token.getUserId();
        // 校验老店铺
        Long shopIdOld = shopConfigQuery.getShopId();
        if (shopIdOld != null && shopIdOld >= 10000000) {
            ShopDTO shopOld = shopService.selectShopById(shopIdOld);
            if (shopOld == null) {
                shopConfigService.removeById(shopIdOld);
                return new Response<>(ERROR, FAILURE + "：店铺不存在");
            }
            if (!TokenType.S.name().equals(token.getType())) {
                boolean hasPermission = hasPermission(userId, shopIdOld, true);
                if (!hasPermission && !Objects.equals(shopOld.getOwnerUserId(), userId)) {
                    return new Response<>(ERROR, FAILURE + "：您没有权限操作该店铺");
                }
            }
        }

        // 校验新店铺
        Long shopIdNew = shopConfigQuery.getShopId();
        if (shopIdNew != null && shopIdNew >= 10000000) {
            ShopDTO shop = shopService.selectShopById(shopIdNew);
            if (shop == null) {
                return new Response<>(ERROR, FAILURE + "：店铺不存在");
            }
            if (!TokenType.S.name().equals(token.getType())) {
                boolean hasPermission = hasPermission(userId, shopIdNew, true);
                if (!hasPermission && !Objects.equals(shop.getOwnerUserId(), userId)) {
                    return new Response<>(ERROR, FAILURE + "：您没有权限操作该店铺");
                }
            }
        } else {
            // 如果没有传入店铺ID，则使用老店铺的ID
            shopConfigQuery.setShopId(shopConfigDTOOld.getShopId());
        }
        if (shopConfigQuery.getLatitude() == null && shopConfigQuery.getLongitude() == null) {
            Point point = shopConfigAddressToPoint(shopConfigQuery);
            if (point != null) {
                shopConfigQuery.setLatitude(point.lat);
                shopConfigQuery.setLongitude(point.lon);
            }
        }
        ShopValidator validator = new ShopValidator();
        if (shopConfigQuery.getName() != null) {
            if (!validator.onShopName(shopConfigQuery.getName()).result()) {
                return new Response<>(ERROR, validator.getErrorMessage());
            }
        }
        if (shopConfigQuery.getAddress() != null) {
            if (!validator.onAddress(shopConfigQuery.getAddress()).result()) {
                return new Response<>(ERROR, validator.getErrorMessage());
            }
        }
        if (shopConfigQuery.getPhoto() != null) {
            if (!validator.onPhoto(shopConfigQuery.getPhoto()).result()) {
                return new Response<>(ERROR, validator.getErrorMessage());
            }
        }
        if (shopConfigQuery.getShippingTimeList() != null && !shopConfigQuery.getShippingTimeList().isEmpty()) {
            if (!validator.onShippingTime(shopConfigQuery.getShippingTimeList()).result()) {
                return new Response<>(ERROR, validator.getErrorMessage());
            }
        }
        ShopConfig shopConfigOld = new ShopConfig(shopConfigDTOOld);
        ShopConfig shopConfigNew = new ShopConfig(shopConfigQuery);
        shopConfigNew.setCreateTime(shopConfigOld.getCreateTime());
        shopConfigNew.setModifyTime(getServerTime());
        Map<String, Object> changes = ReflectDiffUtils.diff(shopConfigOld, shopConfigNew);
        Map<String, Object> params = new HashMap<>();
        params.put("id", shopConfigNew.getId());
        int count = 0;
        boolean isPass = false;
        if (!TokenType.S.name().equals(token.getType())) {
            UserDTO userDTO = userService.findById(userId);
            if (userDTO.getMobile() == null) {
                return new Response<>(ERROR, FAILURE + "：您未绑定过手机号");
            }
            WechatAppDTO wechatAppDTO = wechatAppService.findById(10000000L);
            if (wechatAppDTO == null) {
                return new Response<>(ERROR, FAILURE + "：微信小程序未配置,请联系管理员");
            }
            String accessToken = wechatAppDTO.getAccessToken();
            String openID = userDTO.getOpenid();
            String msgCheckUrl = "/wxa/msg_sec_check?access_token=" + accessToken;
            WeChatMsgSecCheckResponse response = null;
            if (changes.containsKey("description") || changes.containsKey("name")) {
                WechatClient<WeChatMsgSecCheckResponse> checkResponseWechatClient = new WechatClient<>();
                checkResponseWechatClient.setMethod(HttpMethod.POST);
                WeChatMsgSecCheckRequest request = new WeChatMsgSecCheckRequest();
                request.setScene(MsgCheckScene.PROFILE.getCode());
                request.setOpenid(openID);
                String msgCheckContent = null;
                if (changes.containsKey("name")) {
                    msgCheckContent = ((String) changes.get("name")).trim();
                    request.setTitle(msgCheckContent);
                }
                if (changes.containsKey("description")) {
                    msgCheckContent = ((String) changes.get("description")).trim();
                }
                request.setContent(msgCheckContent);
                response = checkResponseWechatClient.execute(msgCheckUrl, request, WeChatMsgSecCheckResponse.class);
            } else {
                isPass = true;
            }
            if (response != null && response.getErrcode() == 0) {
                // 详细审核处理，目前前端需求没有对详细原因的展示
//            for (MsgSecCheckDetail detail : response.getDetail()) {
//                if (detail.getErrcode() == 0) {
//                    if (MsgCheckSuggest.pass.getCode().equals(detail.getSuggest()) && detail.getProb() >= 90)
//                        continue;
//                }
//            }
                isPass = MsgCheckSuggest.pass.getCode().equals(response.getResult().getSuggest());
            }
            if (isPass && changes.containsKey("photo")) {
                String[] urls = ((String) changes.get("photo")).split(App.COMMA);
                if (urls.length > 0 && !urls[0].isEmpty()) { // 文本部分通过了，但是有图片，单独审核
                    String mediaCheckUrl = "/wxa/media_check_async?access_token=" + accessToken;
                    WechatClient<WeChatMsgSecCheckAsyncResponse> mediaCheckResponseWechatClient = new WechatClient<>();
                    mediaCheckResponseWechatClient.setMethod(HttpMethod.POST);
                    WeChatMsgSecCheckAsyncRequest asyncRequest = new WeChatMsgSecCheckAsyncRequest();
                    asyncRequest.setScene(MsgCheckScene.PROFILE.getCode());
                    asyncRequest.setOpenid(openID);
                    ALiOSSClient ossClient = new ALiOSSClient();
                    Long primaryShopConfigModifyApplyId = 0L;
                    List<ShopConfigModifyApplyDTO> shopConfigModifyApplyDTOList = shopConfigModifyApplyService.findByShopId(shopConfigNew.getShopId());
                    if (!shopConfigModifyApplyDTOList.isEmpty()) {
                        List<Long> ids = new ArrayList<>();
                        for (ShopConfigModifyApplyDTO shopConfigModifyApplyDTO : shopConfigModifyApplyDTOList) {
                            if (shopConfigModifyApplyDTO.getPhoto() != null) {
                                ids.add(shopConfigModifyApplyDTO.getId());
                            }
                        }
                        if (!ids.isEmpty())
                            shopConfigModifyApplyService.removeByIds(ids);
                    }
                    for (int i = 0, urlsLength = urls.length; i < urlsLength; i++) {
                        String url = urls[i];
                        if (url == null || url.isEmpty()) {
                            continue;
                        }
                        ShopConfigModifyApply shopConfigModifyApply = new ShopConfigModifyApply();
                        shopConfigModifyApply.setShopId(shopConfigNew.getShopId());
                        shopConfigModifyApply.setPhoto(primaryShopConfigModifyApplyId + App.COMMA + urlsLength + App.COMMA + i + App.COMMA + url);
                        shopConfigModifyApply.setAudit(ShopConfigModifyAudit.PENDING.getCode());
                        shopConfigModifyApply.setStatus(DataStatus.Y.getCode());
                        count += shopConfigModifyApplyService.create(shopConfigModifyApply);
                        if (i == 0 && primaryShopConfigModifyApplyId == 0L) {
                            primaryShopConfigModifyApplyId = shopConfigModifyApply.getId();
                        }
                        String ossUrl = ossClient.generatePresignedUrl(url);
                        asyncRequest.setMediaUrl(ossUrl);
                        asyncRequest.setMediaType(MsgCheckMediaType.IMAGE.getCode());
                        WeChatMsgSecCheckAsyncResponse mediaCheckResponse = mediaCheckResponseWechatClient.execute(mediaCheckUrl, asyncRequest, WeChatMsgSecCheckAsyncResponse.class);
                        if (mediaCheckResponse.getErrcode() == 0) {
                            String traceId = mediaCheckResponse.getTraceId();
                            Long changeId = shopConfigModifyApply.getId();
                            Audit audit = new Audit();
                            audit.setTraceId(traceId);
                            audit.setModifyId(changeId);
                            audit.setUserId(userId);
                            audit.setType(AuditType.SHOP_MEDIA.getCode());
                            count += auditService.create(audit);
                        }
                    }
                    changes.remove("photo");
                }
            }
            if (changes.containsKey("shipping_time_list")) {
                List<ShopOpenCloseTime> shippingTimeList = shopConfigNew.getShippingTimeList();
                count += shopOpenCloseTimeService.removeByShopId(shopConfigNew.getShopId());
                for (ShopOpenCloseTime openCloseTime : shippingTimeList) {
                    openCloseTime.setShopId(shopConfigNew.getShopId());
                    ShopOpenCloseTime shopOpenCloseTime = new ShopOpenCloseTime();
                    BeanUtils.copyProperties(openCloseTime, shopOpenCloseTime, "status", "id", "modifyTime", "createTime");
                    count += shopOpenCloseTimeService.create(shopOpenCloseTime);
                }
                changes.remove("shipping_time_list");
            }
            params.put("changes", changes);
            if (!isPass && response != null && response.getErrcode() == 0) { // 文本部分未通过审核
                ShopConfigModifyApply shopConfigModifyApply = new ShopConfigModifyApply(shopConfigNew);
                count += shopConfigModifyApplyService.create(shopConfigModifyApply);
                Audit audit = new Audit();
                audit.setTraceId(response.getTraceId());
                audit.setModifyId(shopConfigModifyApply.getId());
                audit.setUserId(userId);
                audit.setType(AuditType.SHOP_TEXT.getCode());
                audit.setSuggest(response.getResult().getSuggest());
                audit.setLabel(String.valueOf(response.getResult().getLabel()));
                count += auditService.create(audit);
            } else { // 文本部分通过了，或者没有修改文本部分且清空店铺图片，直接修改店铺配置
                count += shopConfigService.updatePartialById(params);
            }
        } else {
            count += shopConfigService.updatePartialById(params);
        }
        return count > 0 ? new Response<>(OK, SUCCESS) : new Response<>(ERROR, FAILURE + "：店铺配置修改失败");
    }

    /**
     * 修改门票申请是否自动审核通过的开关
     */
    @RequestMapping(value = "/v1/shop/config/ticket/audit/switch/modify", method = RequestMethod.POST)
    @Token(tokenType = {TokenType.S, TokenType.C})
    public Response<?> modifyTicketAutoAuditSwitch(@RequestBody ShopConfigQuery request) {
        ShopConfigValidator validator = new ShopConfigValidator();
        if (!validator.onShopId(request.getShopId()).onTicketAutoAuditSwitch(request.getTicketAutoAuditSwitch()).result()) {
            return new Response<>(ERROR, validator.getErrorMessage());
        }
        ShopConfigDTO shopConfigDTO = shopConfigService.findByShopId(request.getShopId());
        if (shopConfigDTO == null) {
            return new Response<>(ERROR,  "店铺配置不存在,请检查店铺ID是否有误");
        }
        ShopConfig shopConfig = new ShopConfig();
        shopConfig.setId(shopConfigDTO.getId());
        shopConfig.setTicketAutoAuditSwitch(request.getTicketAutoAuditSwitch());
        shopConfig.setModifyTime(this.getServerTime());
        shopConfigService.modifyById(shopConfig);
        return new Response<>(OK, SUCCESS);
    }

    /**
     * 删除店铺配置
     */
    @RequestMapping(value = "/v1/shop/config/remove", method = RequestMethod.POST)
    @Token(tokenType = {TokenType.S})
    public Response<?> remove(@RequestBody ShopConfigQuery request) {
        int count = shopConfigService.removeById(request.getId());
        count += shopOpenCloseTimeService.removeByShopId(request.getId());
        if (count <= 0) {
            return new Response<>(ERROR, FAILURE + "：店铺配置删除失败");
        }
        return new Response<>(OK, SUCCESS);
    }


    /**
     * 查询我可以维护的店铺列表
     */
    @RequestMapping(value = "/v1/shop/config/all/my/query", method = RequestMethod.POST)
    @Token(tokenType = {TokenType.C})
    public Response<List<ShopConfigWithUserRoleDTO>> queryMy() {
        Long userId = this.getUserToken().getId();
        UserRoleQuery userRoleQuery = new UserRoleQuery();
        userRoleQuery.setUserId(userId);
        userRoleQuery.setStatus(DataStatus.Y.getCode());
        List<UserRoleDTO> userRoleDTOList = userRoleService.findAll(userRoleQuery);
        if (userRoleDTOList.isEmpty()) {
            return new Response<>(new ArrayList<>());
        }
        List<Long> shopIds = new ArrayList<>();
        for (UserRoleDTO userRoleDTO : userRoleDTOList) {
            if (allowedReadRoleIds.contains(userRoleDTO.getRoleId()) && !shopIds.contains(userRoleDTO.getShopId())) {
                shopIds.add(userRoleDTO.getShopId());
            }
        }
        List<ShopDTO> shopDTOS = shopService.selectShopByIds(shopIds);
        if (shopDTOS == null || shopDTOS.isEmpty()) {
            return new Response<>(new ArrayList<>());
        }
        shopIds = new ArrayList<>();
        for (ShopDTO shopDTO : shopDTOS) {
            shopIds.add(shopDTO.getId());
        }
        List<ShopConfigDTO> shopConfigDTOs = shopConfigService.findAllByShopIds(shopIds);
        List<ShopConfigWithUserRoleDTO> result = new ArrayList<>();
        for (ShopConfigDTO shopConfigDTO : shopConfigDTOs) {
            List<UserRoleDTO> matchedUserRoles = new ArrayList<>();
            for (UserRoleDTO userRoleDTO : userRoleDTOList) {
                if (Objects.equals(userRoleDTO.getShopId(), shopConfigDTO.getShopId())) {
                    matchedUserRoles.add(userRoleDTO);
                }
            }
            ShopConfigWithUserRoleDTO shopConfigWithUserRoleDTO = new ShopConfigWithUserRoleDTO(shopConfigDTO, matchedUserRoles);
            result.add(shopConfigWithUserRoleDTO);
        }
        return new Response<>(result);
    }

    /**
     * 全部查询店铺营业时间列表
     */
    @Token(tokenType = {TokenType.S, TokenType.C})
    @RequestMapping(value = "/v1/shop/shippingtime/all/query", method = RequestMethod.POST)
    public Response<List<ShopOpenCloseTimeDTO>> queryAllTime(@RequestBody ShopOpenCloseTimeQuery shopOpenCloseTimeQuery) {
        if (shopOpenCloseTimeQuery.getShopId() == null) {
            return new Response<>(ERROR, FAILURE + "：店铺ID不能为空");
        }
        shopOpenCloseTimeQuery.setStatus(DataStatus.Y.getCode());
        List<ShopOpenCloseTimeDTO> shopOpenCloseTimeDTOs = shopOpenCloseTimeService.findAll(shopOpenCloseTimeQuery);
        return new Response<>(shopOpenCloseTimeDTOs);
    }

    /**
     * 修改店铺营业时间
     */
    @Token(tokenType = {TokenType.S, TokenType.C})
    @RequestMapping(value = "/v1/shop/shippingtime/modify", method = RequestMethod.POST)
    public Response<?> modifyTime(@RequestBody ShopConfigQuery shopConfigQuery) {
        if (shopConfigQuery.getShopId() == null) {
            return new Response<>(ERROR, FAILURE + "：店铺ID不能为空");
        }
        Long shopId = shopConfigQuery.getShopId();
        ShopConfigDTO shopConfigDTO = shopConfigService.findByShopId(shopId);
        if (shopConfigDTO == null || DataStatus.N.getCode().equals(shopConfigDTO.getStatus())) {
            return new Response<>(ERROR, FAILURE + "：店铺配置不存在");
        }
        JsonWebToken token = getRawJsonWebToken();
        if (token != null && !TokenType.S.name().equals(token.getType())) {
            Long userId = Long.valueOf(token.getId());
            boolean hasPermission = hasPermission(userId, shopId, true);
            if (!hasPermission) {
                return new Response<>(ERROR, FAILURE + "：您没有权限操作该店铺");
            }
        }
        List<ShopOpenCloseTimeQuery> shippingTimeList = shopConfigQuery.getShippingTimeList();
        ShopValidator validator = new ShopValidator();
        if (shopConfigQuery.getShippingTimeList() != null && !shopConfigQuery.getShippingTimeList().isEmpty()) {
            if (!validator.onShippingTime(shopConfigQuery.getShippingTimeList()).result()) {
                return new Response<>(ERROR, validator.getErrorMessage());
            }
        }
        int count = shopOpenCloseTimeService.removeByShopId(shopConfigQuery.getShopId());
        for (ShopOpenCloseTimeQuery query : shippingTimeList) {
            query.setShopId(shopConfigQuery.getShopId());
            ShopOpenCloseTime shopOpenCloseTime = new ShopOpenCloseTime();
            BeanUtils.copyProperties(query, shopOpenCloseTime, "status", "id", "modifyTime", "createTime");
            count += shopOpenCloseTimeService.create(shopOpenCloseTime);
        }
        if (count > 0) {
            return new Response<>(OK, SUCCESS);
        }
        return new Response<>(ERROR, FAILURE);
    }
}
