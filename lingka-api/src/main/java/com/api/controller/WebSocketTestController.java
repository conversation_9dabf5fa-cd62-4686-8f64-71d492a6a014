package com.api.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.api.websocket.WebSocketSessionManager;

@RestController
public class WebSocketTestController {

    @Autowired
    private WebSocketSessionManager sessionManager;

    @RequestMapping(value = "/v1/websocket/test/{userId}/{param}")
    public void test(@PathVariable("userId") Long userId,@PathVariable("param") String param){
        sessionManager.sendMessageToUser(userId, param);
    }
}
