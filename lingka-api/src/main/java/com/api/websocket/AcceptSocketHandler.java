package com.api.websocket;

import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.handler.TextWebSocketHandler;

public class AcceptSocket<PERSON>andler extends TextWebSocketHandler {

    // 连接成功
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        Long userId = (Long) session.getAttributes().get("userId");
        System.out.println("用户 " + userId + " 已连接");
    }

    // 处理消息
    public void handleBinaryMessage(WebSocketSession session, WebSocketMessage<?> message) throws Exception {
        Long userId = (Long) session.getAttributes().get("userId");
        System.out.println("收到 " + userId + " 的消息：" + message.getPayload());
        session.sendMessage(new TextMessage("Echo: " + message.getPayload()));
    }

    // 连接关闭
    public void afterConnectionClosed(WebSocketSession session, CloseStatus closeStatus) throws Exception {
        Long userId = (Long) session.getAttributes().get("userId");
        System.out.println("用户 " + userId + " 已断开");
    }
}
