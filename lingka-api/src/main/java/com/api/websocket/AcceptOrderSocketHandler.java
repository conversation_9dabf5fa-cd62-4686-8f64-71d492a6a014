package com.api.websocket;

import java.io.IOException;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.socket.BinaryMessage;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.handler.TextWebSocketHandler;

public class AcceptOrderSocketHandler extends TextWebSocketHandler {

    @Autowired
    private WebSocketSessionManager sessionManager;

    // 连接成功
    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        Long userId = (Long) session.getAttributes().get("userId");
        System.out.println("WebSocket连接建立 - 用户ID: " + userId + ", 会话ID: " + session.getId());
        System.out.println("SessionManager是否为null: " + (sessionManager == null));

        // 将会话添加到管理器中
        if (userId != null && sessionManager != null) {
            sessionManager.addSession(userId, session);
        } else if (sessionManager == null) {
            System.err.println("SessionManager为null，无法添加会话！");
        }
    }

    // 处理文本消息
    @Override
    public void handleTextMessage(WebSocketSession session, TextMessage message) throws Exception {
        Long userId = (Long) session.getAttributes().get("userId");
        System.out.println("收到 " + userId + " 的文本消息：" + message.getPayload());
        session.sendMessage(new TextMessage("Echo: " + message.getPayload()));
    }

//    // 处理二进制消息
//    @Override
//    public void handleBinaryMessage(WebSocketSession session, BinaryMessage message) {
//        Long userId = (Long) session.getAttributes().get("userId");
//        System.out.println("收到 " + userId + " 的二进制消息，长度：" + message.getPayload().remaining());
//        // 回显二进制消息
//        try {
//            session.sendMessage(message);
//        } catch (IOException e) {
//            throw new RuntimeException(e);
//        }
//    }

    // 连接关闭
    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus closeStatus) throws Exception {
        Long userId = (Long) session.getAttributes().get("userId");
        System.out.println("WebSocket连接关闭 - 用户ID: " + userId + ", 会话ID: " + session.getId() + ", 关闭状态: " + closeStatus);

        // 从管理器中移除会话
        if (userId != null) {
            sessionManager.removeSession(userId, session);
        }
    }
}
