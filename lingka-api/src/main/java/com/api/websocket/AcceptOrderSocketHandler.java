package com.api.websocket;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.handler.TextWebSocketHandler;

public class AcceptOrderSocketHandler extends TextWebSocketHandler {
    Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private WebSocketSessionManager sessionManager;

    // 连接成功
    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        Long userId = (Long) session.getAttributes().get("userId");
        logger.info("WebSocket连接建立 - 用户ID: {}, 会话ID: {}", userId, session.getId());
        // 将会话添加到管理器中
        if (userId != null) {
            sessionManager.addSession(userId, session);
        }
    }

    // 处理文本消息
    @Override
    public void handleTextMessage(WebSocketSession session, TextMessage message) throws Exception {
        Long userId = (Long) session.getAttributes().get("userId");
        logger.info("收到 {}  的文本消息：{}", userId, message.getPayload());
    }

//    // 处理二进制消息
//    @Override
//    public void handleBinaryMessage(WebSocketSession session, BinaryMessage message) {
//        Long userId = (Long) session.getAttributes().get("userId");
//        System.out.println("收到 " + userId + " 的二进制消息，长度：" + message.getPayload().remaining());
//        // 回显二进制消息
//        try {
//            session.sendMessage(message);
//        } catch (IOException e) {
//            throw new RuntimeException(e);
//        }
//    }

    // 连接关闭
    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus closeStatus) throws Exception {
        Long userId = (Long) session.getAttributes().get("userId");
        logger.info("WebSocket连接关闭 - 用户ID: {}, 会话ID: {}, 关闭状态: {}", userId, session.getId(), closeStatus);
        // 从管理器中移除会话
        if (userId != null) {
            sessionManager.removeSession(userId, session);
        }
    }
}
