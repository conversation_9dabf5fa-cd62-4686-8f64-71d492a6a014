package com.api.websocket;

import java.io.IOException;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArraySet;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;

@Component
public class WebSocketSessionManager {

    final Logger logger = LoggerFactory.getLogger(this.getClass());

    // 存储所有在线用户的WebSocket会话
    private static final ConcurrentHashMap<Long, CopyOnWriteArraySet<WebSocketSession>> USER_SESSIONS = new ConcurrentHashMap<>();

    /**
     * 添加用户会话
     */
    public void addSession(Long userId, WebSocketSession session) {
        USER_SESSIONS.computeIfAbsent(userId, k -> new CopyOnWriteArraySet<>()).add(session);
        logger.info("用户{} 添加会话，当前在线会话数 {} ", userId, USER_SESSIONS.get(userId).size());
    }

    /**
     * 移除用户会话
     */
    public void removeSession(Long userId, WebSocketSession session) {
        CopyOnWriteArraySet<WebSocketSession> sessions = USER_SESSIONS.get(userId);
        if (sessions != null) {
            sessions.remove(session);
            if (sessions.isEmpty()) {
                USER_SESSIONS.remove(userId);
            }
            logger.info("用户{} 移除会话", userId);
        }
    }

    /**
     * 向指定用户发送消息
     */
    public void sendMessageToUser(Long userId, String message) {
        CopyOnWriteArraySet<WebSocketSession> sessions = USER_SESSIONS.get(userId);
        if (sessions != null && !sessions.isEmpty()) {
            TextMessage textMessage = new TextMessage(message);
            sessions.forEach(session -> {
                try {
                    if (session.isOpen()) {
                        session.sendMessage(textMessage);
                        logger.info("向用户 {}  发送消息：{}", userId, message);
                    }
                } catch (IOException e) {
                    logger.error("向用户 {} 发送消息失败：{}", userId, e.getMessage());
                    // 移除无效会话
                    sessions.remove(session);
                }
            });
        } else {
            logger.info("用户 {} 不在线，无法发送消息", userId);
        }
    }

    /**
     * 向所有在线用户广播消息
     */
    public void broadcastMessage(String message) {
        USER_SESSIONS.forEach((userId, sessions) -> {
            sendMessageToUser(userId, message);
        });
    }

    /**
     * 获取在线用户数量
     */
    public int getOnlineUserCount() {
        return USER_SESSIONS.size();
    }

    /**
     * 检查用户是否在线
     */
    public boolean isUserOnline(Long userId) {
        CopyOnWriteArraySet<WebSocketSession> sessions = USER_SESSIONS.get(userId);
        return sessions != null && !sessions.isEmpty();
    }
}
