<!DOCTYPE html>
<html>
<head>
    <title>WebSocket 测试</title>
    <meta charset="UTF-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .connected { background-color: #d4edda; color: #155724; }
        .disconnected { background-color: #f8d7da; color: #721c24; }
        .message-area { border: 1px solid #ccc; height: 300px; overflow-y: auto; padding: 10px; margin: 10px 0; }
        .input-area { margin: 10px 0; }
        input[type="text"] { width: 70%; padding: 5px; }
        button { padding: 5px 15px; margin: 0 5px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>WebSocket 连接测试</h1>
        
        <div id="status" class="status disconnected">未连接</div>
        
        <div class="input-area">
            <input type="text" id="tokenInput" placeholder="请输入JWT Token (可选)" style="width: 90%;">
        </div>
        
        <div class="input-area">
            <button onclick="connect()">连接</button>
            <button onclick="disconnect()">断开连接</button>
        </div>
        
        <div class="input-area">
            <input type="text" id="messageInput" placeholder="输入消息">
            <button onclick="sendMessage()">发送消息</button>
        </div>
        
        <div class="message-area" id="messages"></div>
    </div>

    <script>
        let ws = null;
        
        function addMessage(message, type = 'info') {
            const messagesDiv = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            const timestamp = new Date().toLocaleTimeString();
            messageDiv.innerHTML = `<strong>[${timestamp}] ${type.toUpperCase()}:</strong> ${message}`;
            messageDiv.style.color = type === 'error' ? 'red' : type === 'sent' ? 'blue' : 'green';
            messagesDiv.appendChild(messageDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }
        
        function updateStatus(connected) {
            const statusDiv = document.getElementById('status');
            if (connected) {
                statusDiv.textContent = '已连接到 WebSocket';
                statusDiv.className = 'status connected';
            } else {
                statusDiv.textContent = '未连接';
                statusDiv.className = 'status disconnected';
            }
        }
        
        function connect() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                addMessage('WebSocket 已经连接', 'info');
                return;
            }
            
            const token = document.getElementById('tokenInput').value;
            const wsUrl = 'ws://localhost:8080/ws/chat';
            
            try {
                ws = new WebSocket(wsUrl);
                
                // 如果有token，在连接建立后发送
                ws.onopen = function(event) {
                    addMessage('WebSocket 连接已建立', 'info');
                    updateStatus(true);
                    
                    if (token) {
                        // 这里可以发送认证信息，但由于我们的拦截器是在握手阶段处理的
                        // 所以token需要通过header发送，这里只是演示
                        addMessage('注意：Token需要通过HTTP Header发送，当前连接可能没有用户ID', 'info');
                    }
                };
                
                ws.onmessage = function(event) {
                    addMessage('收到消息: ' + event.data, 'received');
                };
                
                ws.onclose = function(event) {
                    addMessage('WebSocket 连接已关闭. Code: ' + event.code + ', Reason: ' + event.reason, 'info');
                    updateStatus(false);
                };
                
                ws.onerror = function(error) {
                    addMessage('WebSocket 错误: ' + error, 'error');
                    updateStatus(false);
                };
                
            } catch (error) {
                addMessage('连接失败: ' + error.message, 'error');
            }
        }
        
        function disconnect() {
            if (ws) {
                ws.close();
                ws = null;
            }
        }
        
        function sendMessage() {
            const messageInput = document.getElementById('messageInput');
            const message = messageInput.value.trim();
            
            if (!message) {
                addMessage('请输入消息', 'error');
                return;
            }
            
            if (!ws || ws.readyState !== WebSocket.OPEN) {
                addMessage('WebSocket 未连接', 'error');
                return;
            }
            
            try {
                ws.send(message);
                addMessage('发送消息: ' + message, 'sent');
                messageInput.value = '';
            } catch (error) {
                addMessage('发送失败: ' + error.message, 'error');
            }
        }
        
        // 回车发送消息
        document.getElementById('messageInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });
        
        // 页面关闭时断开连接
        window.addEventListener('beforeunload', function() {
            if (ws) {
                ws.close();
            }
        });
    </script>
</body>
</html>
