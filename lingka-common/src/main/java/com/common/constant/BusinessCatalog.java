package com.common.constant;

public enum BusinessCatalog {
	USER_TICKET_APPLY("1000","报名消息"),
	ORDER("1001","订单消息"),
	STAFF("1002","员工消息");

	private String code;
	private String name;
	BusinessCatalog(String code, String name){
		this.code = code;
		this.name = name;
	}
	public String getCode() {
		return code;
	}
	public void setCode(String code) {
		this.code = code;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public static String getName(String code){
		for (BusinessCatalog value : BusinessCatalog.values()) {
            if (value.getCode().equals(code)) {
                return value.name;
            }
        }
		return null;
	}
}
