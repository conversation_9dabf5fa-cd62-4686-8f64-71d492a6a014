package com.common.constant;

public enum UserMessageReadStage {
    Y("0", "已读"),
    N("1", "未读");

    private String code;
    private String name;

    UserMessageReadStage(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static String getName(String code) {
        for (UserMessageReadStage value : UserMessageReadStage.values()) {
            if (value.getCode().equals(code)) {
                return value.name;
            }
        }
        return null;
    }
}
