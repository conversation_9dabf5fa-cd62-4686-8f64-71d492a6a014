package com.common.constant;

public enum UserMessageReadStage {
    Y("0", "已读"),
    N("1", "未读");

    private final String code;
    private final String description;

    UserMessageReadStage(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
