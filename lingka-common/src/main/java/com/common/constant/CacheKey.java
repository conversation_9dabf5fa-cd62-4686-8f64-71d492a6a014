package com.common.constant;

public class CacheKey {

	/**
	 * 用户Token
	 */
	public static final String USER_TOKEN = "CK1000";


	/**
	 * 系统用户登录限制
	 */
	public static final String SYS_USER_LOGIN_LIMIT = "CK1001";


	/**
	 * 文件导出
	 */
	public static final String FILE_EXPORT = "CK1002";

	/**
	 * 文件的下载地址
	 */
	public static final String FILE_DOWNLOAD_URL = "CK1003";

	/**
	 * 用户登录限制
	 */
	public static final String LOCK_USER_LOGIN = "CK1004";

	/**
	 * 用户绑定手机号限制
	 */
	public static final String LOCK_USER_MOBILE_BIND = "CK1005";


	/**
	 * 店铺修改产品锁
	 */
	public static final String LOCK_PRODUCT = "CK1006";

	/**
	 * 店铺修改产品锁
	 */
	public static final String LOCK_USER_CART = "CK1007";

	/**
	 * 用户下单锁
	 */
	public static final String LOCK_USER_ORDER = "CK1008";

	/**
	 * 用户审核门票锁
	 */
	public static final String LOCK_USER_TICKET_APPLY = "CK1009";

	/**
	 * 用户门票锁
	 */
	public static final String LOCK_USER_TICKET = "CK1010";


	/**
	 * 用户转赠门票锁
	 */
	public static final String LOCK_USER_TICKET_GIFT = "CK1011";

	/**
	 * 用户消息队列
	 */
	public static final String USER_MESSAGE_QUEUE = "CK1012";


	/**
	 * 订单支付锁
	 */
	public static final String LOCK_ORDER_PAYMENT = "CK1013";


}

