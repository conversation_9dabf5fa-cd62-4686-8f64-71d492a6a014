package com.common.constant;

public enum OrderPayStage {
	Y("0","已支付"),
	N("1","未支付"),
	REFUND("2","已退款");
	private String code;
	private String name;
	OrderPayStage(String code, String name){
		this.code = code;
		this.name = name;
	}
	public String getCode() {
		return code;
	}
	public void setCode(String code) {
		this.code = code;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public static String getCode(String name){
		for (OrderPayStage value : OrderPayStage.values()) {
            if (value.getName().equals(name)) {
                return value.code;
            }
        }
		return null;
	}
	public static String getName(String code){
		for (OrderPayStage value : OrderPayStage.values()) {
            if (value.getCode().equals(code)) {
                return value.name;
            }
        }
		return null;
	}
}
