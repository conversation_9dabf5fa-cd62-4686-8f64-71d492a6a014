package com.common.constant;

public enum SubOrderStage {
	WAIT_ACCEPT("10","待接单"),
	PRODUCTION("11","制作中"),
	FINISH("12","已完成"),
	REFUND("13","退款"),
	CANCEL("14","已取消");
	private String code;
	private String name;
	SubOrderStage(String code, String name){
		this.code = code;
		this.name = name;
	}
	public String getCode() {
		return code;
	}
	public void setCode(String code) {
		this.code = code;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}

	public static String getCode(String name){
		for (SubOrderStage gender : SubOrderStage.values()) {
            if (gender.getName().equals(name)) {
                return gender.code;
            }
        }
		return null;
	}
	public static String getName(String code){
		for (SubOrderStage gender : SubOrderStage.values()) {
            if (gender.getCode().equals(code)) {
                return gender.name;
            }
        }
		return null;
	}
}
