package com.common.bean;

import java.io.Serializable;

public class UserMessagePayload implements Serializable {


    private static final long serialVersionUID = 6410854721291225295L;

    /** 用户ID */
    private Long userId;

    /** 店铺ID */
    private Long shopId;

    /** 消息标题 */
    private String title;

    /** 消息内容 */
    private String content;

    /** 业务分类 */
    private String businessCatalog;

    /** 业务ID */
    private Long businessId;


    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getBusinessCatalog() {
        return businessCatalog;
    }

    public void setBusinessCatalog(String businessCatalog) {
        this.businessCatalog = businessCatalog;
    }

    public Long getBusinessId() {
        return businessId;
    }

    public void setBusinessId(Long businessId) {
        this.businessId = businessId;
    }
}
