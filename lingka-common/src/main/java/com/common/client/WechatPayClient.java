package com.common.client;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.wechat.pay.java.core.Config;
import com.wechat.pay.java.core.RSAPublicKeyConfig;
import com.wechat.pay.java.service.partnerpayments.jsapi.JsapiService;
import com.wechat.pay.java.service.partnerpayments.jsapi.model.Amount;
import com.wechat.pay.java.service.partnerpayments.jsapi.model.PrepayRequest;
import com.wechat.pay.java.service.partnerpayments.jsapi.model.PrepayResponse;

public class WechatPayClient {



    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    /** 商户号 */
    public static final String merchantId = "1723366968";
    /** 商户API公钥路径 */
    public static final String publicKeyPath = "/Users/<USER>/cert/pub_key.pem";
    /** 商户API私钥路径 */
    public static final String privateKeyPath = "/Users/<USER>/cert/1723366968_20250805_cert/apiclient_key.pem";
    /** 商户证书序列号 */
    public static final String merchantSerialNumber = "759336FCD4E527FEC775933C2A517579FA6DE613";
    /** 商户API证书公钥ID */
    public static final String publicKeyId = "PUB_KEY_ID_0117233669682025080500112075002400";
    /** 商户APIV3密钥 */
    public static String apiV3Key = "...";

    public static void main(String[] args) {
        // 使用微信支付公钥的RSA配置
        Config config =
                new RSAPublicKeyConfig.Builder()
                        .merchantId(merchantId)
                        .privateKeyFromPath(privateKeyPath)
                        .publicKeyFromPath(publicKeyPath)
                        .publicKeyId(publicKeyId)
                        .merchantSerialNumber(merchantSerialNumber)
                       // .apiV3Key(apiV3Key)
                        .build();
        // 构建service
        JsapiService service = new JsapiService.Builder().config(config).build();
        // request.setXxx(val)设置所需参数，具体参数可见Request定义
        PrepayRequest request = new PrepayRequest();
        Amount amount = new Amount();
        amount.setTotal(100);
        request.setAmount(amount);
        // 服务商APPID
        request.setSpAppid("wxa9d9651ae******");
//        request.setMchid("190000****");
        request.setDescription("测试商品标题");
        request.setNotifyUrl("https://notify_url");
        request.setOutTradeNo("out_trade_no_001");
        // 调用下单方法，得到应答
        PrepayResponse response = service.prepay(request);
        // 使用微信扫描 code_url 对应的二维码，即可体验Native支付
        System.out.println(response.getPrepayId());
    }

    public JsapiService getJsapiService(){
        // 使用微信支付公钥的RSA配置
        Config config =
                new RSAPublicKeyConfig.Builder()
                        .merchantId(merchantId)
                        .privateKeyFromPath(privateKeyPath)
                        .publicKeyFromPath(publicKeyPath)
                        .publicKeyId(publicKeyId)
                        .merchantSerialNumber(merchantSerialNumber)
                        // .apiV3Key(apiV3Key)
                        .build();
        // 构建service
        return new JsapiService.Builder().config(config).build();
    }


    String getJSON(Object obj){
        ObjectMapper objectMapper = new ObjectMapper();
        try{
            return objectMapper.writeValueAsString(obj);
        }catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
